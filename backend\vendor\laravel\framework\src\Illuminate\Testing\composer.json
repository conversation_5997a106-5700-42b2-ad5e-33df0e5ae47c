{"name": "illuminate/testing", "description": "The Illuminate Testing package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-mbstring": "*", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Testing\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"brianium/paratest": "Required to run tests in parallel (^6.0).", "illuminate/console": "Required to assert console commands (^10.0).", "illuminate/database": "Required to assert databases (^10.0).", "illuminate/http": "Required to assert responses (^10.0).", "mockery/mockery": "Required to use mocking (^1.5.1).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8|^10.0.7)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}