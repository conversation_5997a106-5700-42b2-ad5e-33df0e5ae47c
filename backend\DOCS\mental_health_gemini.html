<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ases<PERSON></title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom styles for print to ensure readability */
        @media print {
            body {
                background: none !important;
            }
            .container {
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: none !important;
            }
            /* Hide navigation and utility elements during print */
            .flex.justify-between.items-center,
            .flex.items-center.justify-center.mb-4,
            .flex.flex-wrap.justify-center.gap-4,
            button {
                display: none !important;
            }
            footer {
                display: none !important;
            }
            /* Ensure results content is visible and well-formatted */
            #results-content {
                box-shadow: none !important;
                border: none !important;
                background: white !important;
                padding: 0 !important;
            }
            .ResultCard {
                margin-bottom: 1rem !important;
            }
            h1, h2, h3, p {
                color: black !important; /* Ensure text is black for printing */
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <!-- Babel for JSX transformation in browser (for development, not production) -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <!-- Lucide React Icons (requires React and ReactDOM) -->
    <script type="module">
        import { ChevronsLeft, ChevronsRight, FileText, HeartPulse, BrainCircuit, Repeat, Database, Printer, FileDown, FileText as FileTextIcon } from 'https://unpkg.com/lucide-react@0.292.0/dist/lucide-react.js';
        window.lucideReact = { ChevronsLeft, ChevronsRight, FileText, HeartPulse, BrainCircuit, Repeat, Database, Printer, FileDown, FileTextIcon };
    </script>
    <!-- Firebase CDN -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, doc, setDoc, collection, query, where, getDocs, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        window.firebase = { initializeApp, getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged };
        window.firestore = { getFirestore, doc, setDoc, collection, query, where, getDocs, serverTimestamp };

        // Define global variables for Firebase config and auth token
        // These would typically be provided by your environment in a real deployment
        window.__app_id = 'mental-health-assessment-app'; // Replace with your actual app ID
        window.__firebase_config = JSON.stringify({
            apiKey: "YOUR_API_KEY", // Replace with your Firebase API Key
            authDomain: "YOUR_AUTH_DOMAIN", // Replace with your Firebase Auth Domain
            projectId: "YOUR_PROJECT_ID", // Replace with your Firebase Project ID
            storageBucket: "YOUR_STORAGE_BUCKET", // Replace with your Firebase Storage Bucket
            messagingSenderId: "YOUR_MESSAGING_SENDER_ID", // Replace with your Firebase Messaging Sender ID
            appId: "YOUR_APP_ID" // Replace with your Firebase App ID
        });
        window.__initial_auth_token = ''; // Leave empty if not using custom tokens, or provide one
    </script>
    <!-- html2pdf.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

    <script type="text/babel">
        const { useState, useMemo, useEffect } = React;
        const { ChevronsLeft, ChevronsRight, FileText, HeartPulse, BrainCircuit, Repeat, Database, Printer, FileDown, FileTextIcon } = window.lucideReact;
        const { initializeApp, getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } = window.firebase;
        const { getFirestore, doc, setDoc, collection, query, where, getDocs, serverTimestamp } = window.firestore;

        // --- Data Kuesioner (dalam Bahasa Indonesia) ---

        const srqQuestions = [
            { id: 'srq1', text: 'Apakah Anda sering merasa sakit kepala?' },
            { id: 'srq2', text: 'Apakah Anda kehilangan nafsu makan?' },
            { id: 'srq3', text: 'Apakah tidur Anda tidak nyenyak?' },
            { id: 'srq4', text: 'Apakah Anda mudah merasa takut?' },
            { id: 'srq5', text: 'Apakah Anda merasa cemas, tegang, atau khawatir?' },
            { id: 'srq6', text: 'Apakah tangan Anda gemetar?' },
            { id: 'srq7', text: 'Apakah pencernaan Anda terganggu atau tidak lancar?' },
            { id: 'srq8', text: 'Apakah Anda sulit berpikir jernih?' },
            { id: 'srq9', text: 'Apakah Anda merasa tidak bahagia?' },
            { id: 'srq10', text: 'Apakah Anda lebih sering menangis?' },
            { id: 'srq11', text: 'Apakah Anda merasa sulit untuk menikmati aktivitas sehari-hari?' },
            { id: 'srq12', text: 'Apakah Anda mengalami kesulitan untuk mengambil keputusan?' },
            { id: 'srq13', text: 'Apakah pekerjaan atau tugas sehari-hari Anda terbengkalai?' },
            { id: 'srq14', text: 'Apakah Anda merasa tidak mampu berperan dalam kehidupan ini?' },
            { id: 'srq15', text: 'Apakah Anda kehilangan minat pada banyak hal?' },
            { id: 'srq16', text: 'Apakah Anda merasa tidak berharga?' },
            { id: 'srq17', text: 'Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?' },
            { id: 'srq18', text: 'Apakah Anda merasa lelah sepanjang waktu?' },
            { id: 'srq19', text: 'Apakah Anda merasakan rasa tidak enak di perut?' },
            { id: 'srq20', text: 'Apakah Anda mudah lelah?' },
        ];

        const phqQuestions = [
            { id: 'phq1', text: 'Kurang berminat atau bergairah dalam melakukan apapun' },
            { id: 'phq2', text: 'Merasa murung, sedih, atau putus asa' },
            { id: 'phq3', text: 'Sulit tidur atau mudah terbangun, atau terlalu banyak tidur' },
            { id: 'phq4', text: 'Merasa lelah atau kurang bertenaga' },
            { id: 'phq5', text: 'Kurang nafsu makan atau terlalu banyak makan' },
            { id: 'phq6', text: 'Merasa buruk tentang diri sendiri - atau bahwa Anda adalah seorang yang gagal atau telah mengecewakan diri sendiri atau keluarga' },
            { id: 'phq7', text: 'Sulit berkonsentrasi pada sesuatu, seperti membaca koran atau menonton televisi' },
            { id: 'phq8', text: 'Bergerak atau berbicara sangat lambat sehingga orang lain bisa memperhatikan. Atau sebaliknya - menjadi sangat resah atau gelisah sehingga Anda lebih sering bergerak dari biasanya' },
            { id: 'phq9', text: 'Pikiran bahwa Anda akan lebih baik mati, atau ingin menyakiti diri sendiri dengan cara apapun' },
        ];

        const sdqQuestions = [
            // Emotional Problems
            { id: 'sdq1', text: 'Sering mengeluh sakit kepala, sakit perut atau macam-macam sakit lainnya', scale: 'emotional' },
            { id: 'sdq2', text: 'Banyak kekhawatiran, sering tampak khawatir', scale: 'emotional' },
            { id: 'sdq3', text: 'Sering tidak bahagia, sedih atau menangis', scale: 'emotional' },
            { id: 'sdq4', text: 'Gugup atau mudah hilang percaya diri pada situasi baru', scale: 'emotional' },
            { id: 'sdq5', text: 'Mudah takut atau gampang terkejut', scale: 'emotional' },
            // Conduct Problems
            { id: 'sdq6', text: 'Sering marah meledak-ledak atau mengamuk', scale: 'conduct' },
            { id: 'sdq7', text: 'Umumnya patuh, biasanya melakukan apa yang disuruh orang dewasa', scale: 'conduct', reverse: true },
            { id: 'sdq8', text: 'Sering berkelahi dengan anak-anak lain atau mengintimidasi mereka', scale: 'conduct' },
            { id: 'sdq9', text: 'Sering berbohong atau berbuat curang', scale: 'conduct' },
            { id: 'sdq10', text: 'Mencuri dari rumah, sekolah atau tempat lain', scale: 'conduct' },
            // Hyperactivity
            { id: 'sdq11', text: 'Gelisah, terlalu aktif, tidak dapat diam untuk waktu lama', scale: 'hyperactivity' },
            { id: 'sdq12', text: 'Terus-menerus bergerak dengan resah atau menggeliat', scale: 'hyperactivity' },
            { id: 'sdq13', text: 'Mudah teralih perhatiannya, tidak dapat berkonsentrasi', scale: 'hyperactivity' },
            { id: 'sdq14', text: 'Berpikir sebelum bertindak', scale: 'hyperactivity', reverse: true },
            { id: 'sdq15', text: 'Menyelesaikan tugas yang sedang dikerjakan sampai selesai, rentang perhatian baik', scale: 'hyperactivity', reverse: true },
            // Peer Problems
            { id: 'sdq16', text: 'Cenderung menyendiri, lebih suka bermain sendiri', scale: 'peer' },
            { id: 'sdq17', text: 'Punya sedikitnya satu teman baik', scale: 'peer', reverse: true },
            { id: 'sdq18', text: 'Secara umum disukai oleh anak-anak lain', scale: 'peer', reverse: true },
            { id: 'sdq19', text: 'Diganggu atau diintimidasi oleh anak-anak lain', scale: 'peer' },
            { id: 'sdq20', text: 'Bergaul lebih baik dengan orang dewasa daripada dengan anak-anak lain', scale: 'peer' },
            // Prosocial
            { id: 'sdq21', text: 'Memperhatikan perasaan orang lain', scale: 'prosocial' },
            { id: 'sdq22', text: 'Bersedia berbagi dengan anak-anak lain (misalnya, mainan, makanan)', scale: 'prosocial' },
            { id: 'sdq23', text: 'Suka menolong jika seseorang terluka, kecewa atau sakit', scale: 'prosocial' },
            { id: 'sdq24', text: 'Baik terhadap anak-anak yang lebih muda', scale: 'prosocial' },
            { id: 'sdq25', text: 'Sering menawarkan diri untuk membantu orang lain (orang tua, guru, anak-anak lain)', scale: 'prosocial' },
        ];

        const phqOptions = [
            { label: 'Tidak pernah', value: 0 },
            { label: 'Beberapa hari', value: 1 },
            { label: 'Lebih dari separuh', value: 2 },
            { label: 'Hampir setiap hari', value: 3 },
        ];

        const sdqOptions = [
            { label: 'Tidak Benar', value: 0 },
            { label: 'Agak Benar', value: 1 },
            { label: 'Sangat Benar', value: 2 },
        ];

        // --- Komponen-komponen UI ---

        const WelcomeScreen = ({ onStart }) => (
            <div className="text-center">
                <BrainCircuit className="mx-auto h-24 w-24 text-blue-500" />
                <h1 className="mt-6 text-3xl md:text-4xl font-bold text-gray-800">Asesmen Kesehatan Mental Mandiri</h1>
                <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
                    Selamat datang! Formulir ini dirancang untuk membantumu memahami kondisi emosional dan psikologis-mu saat ini. Jawablah setiap pertanyaan dengan jujur. Tidak ada jawaban yang benar atau salah.
                </p>
                <div className="mt-8 p-4 bg-blue-50 border-l-4 border-blue-500 text-blue-800 rounded-r-lg">
                    <p className="font-semibold">Penting: Kerahasiaan Terjamin</p>
                    <p className="text-sm mt-1">Hasil asesmen ini bersifat rahasia dan hanya untuk membantumu. Ini bukan diagnosis medis. Jika kamu merasa butuh bantuan, jangan ragu untuk berbicara dengan guru BK, orang tua, atau profesional kesehatan mental.</p>
                </div>
                <button
                    onClick={onStart}
                    className="mt-8 inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white font-bold rounded-full shadow-lg hover:bg-blue-700 transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300"
                >
                    Mulai Asesmen <ChevronsRight className="ml-2 h-5 w-5" />
                </button>
            </div>
        );

        const QuestionnaireStep = ({ title, description, questions, options, answers, onAnswerChange, questionType }) => {
            return (
                <div>
                    <h2 className="text-2xl font-bold text-gray-800">{title}</h2>
                    <p className="mt-2 text-gray-600">{description}</p>
                    <div className="mt-8 space-y-8">
                        {questions.map((q, index) => (
                            <div key={q.id} className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                <p className="font-semibold text-gray-700">{index + 1}. {q.text}</p>
                                <div className="mt-4 flex flex-wrap gap-4">
                                    {options.map(opt => (
                                        <label key={opt.value} className="flex items-center space-x-2 cursor-pointer">
                                            <input
                                                type="radio"
                                                name={q.id}
                                                value={opt.value}
                                                checked={answers[q.id] === opt.value}
                                                onChange={() => onAnswerChange(q.id, opt.value)}
                                                className="form-radio h-5 w-5 text-blue-600 transition duration-150 ease-in-out"
                                            />
                                            <span className="text-gray-700">{opt.label}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            );
        };

        const ResultsScreen = ({ answers, onRestart }) => {
            // Calculate SRQ score: count answers that are '1' (Ya)
            const srqScore = useMemo(() => Object.values(answers.srq).filter(ans => ans === 1).length, [answers.srq]);
            // Calculate PHQ score: sum up all answer values
            const phqScore = useMemo(() => Object.values(answers.phq).reduce((sum, val) => sum + val, 0), [answers.phq]);
            // Calculate SDQ scores for each scale and total difficulties
            const sdqScores = useMemo(() => {
                const scores = { emotional: 0, conduct: 0, hyperactivity: 0, peer: 0, prosocial: 0, total: 0 };
                sdqQuestions.forEach(q => {
                    let value = answers.sdq[q.id] || 0;
                    // Reverse score for specific questions
                    if (q.reverse) {
                        if (value === 0) value = 2;
                        else if (value === 2) value = 0;
                    }
                    scores[q.scale] += value;
                });
                // Total difficulties score is sum of emotional, conduct, hyperactivity, and peer problems
                scores.total = scores.emotional + scores.conduct + scores.hyperactivity + scores.peer;
                return scores;
            }, [answers.sdq]);

            // Function to get interpretation based on questionnaire type and score
            const getInterpretation = (type, score, scores) => {
                switch (type) {
                    case 'SRQ-29':
                        if (score <= 7) return { text: 'Tidak terindikasi adanya masalah kesehatan jiwa yang signifikan.', color: 'green', risk: 'Rendah' };
                        return { text: 'Terindikasi adanya masalah kesehatan jiwa. Disarankan untuk berkonsultasi lebih lanjut.', color: 'red', risk: 'Tinggi' };
                    case 'PHQ-9':
                        if (score <= 4) return { text: 'Gejala depresi minimal atau tidak ada.', color: 'green', risk: 'Minimal' };
                        if (score <= 9) return { text: 'Gejala depresi ringan.', color: 'yellow', risk: 'Ringan' };
                        if (score <= 14) return { text: 'Gejala depresi sedang.', color: 'orange', risk: 'Sedang' };
                        if (score <= 19) return { text: 'Gejala depresi sedang-berat.', color: 'red', risk: 'Sedang-Berat' };
                        return { text: 'Gejala depresi berat. Sangat disarankan untuk segera mencari bantuan profesional.', color: 'red', risk: 'Berat' };
                    case 'SDQ-25':
                        if (score.total <= 13) return { text: 'Skor kesulitan berada dalam rentang normal.', color: 'green', risk: 'Normal' };
                        if (score.total <= 16) return { text: 'Skor kesulitan berada di ambang batas (borderline).', color: 'yellow', risk: 'Ambang Batas' };
                        return { text: 'Skor kesulitan berada dalam rentang abnormal. Perlu perhatian lebih lanjut.', color: 'red', risk: 'Abnormal' };
                    case 'SDQ-Prosocial':
                        if (score.prosocial >= 6) return { text: 'Kekuatan prososial berada dalam rentang normal.', color: 'green', risk: 'Normal' };
                        if (score.prosocial === 5) return { text: 'Kekuatan prososial berada di ambang batas (borderline).', color: 'yellow', risk: 'Ambang Batas' };
                        return { text: 'Kekuatan prososial berada dalam rentang abnormal. Perlu perhatian lebih lanjut.', color: 'red', risk: 'Abnormal' };
                    default: return { text: '', color: 'gray' };
                }
            };

            // Get interpretations for each questionnaire
            const interpretations = {
                srq: getInterpretation('SRQ-29', srqScore),
                phq: getInterpretation('PHQ-9', phqScore),
                sdq: getInterpretation('SDQ-25', sdqScores),
                sdqProsocial: getInterpretation('SDQ-Prosocial', sdqScores),
            };
            
            // Tailwind CSS classes for different result colors
            const colorClasses = {
                green: 'bg-green-100 border-green-500 text-green-800',
                yellow: 'bg-yellow-100 border-yellow-500 text-yellow-800',
                orange: 'bg-orange-100 border-orange-500 text-orange-800',
                red: 'bg-red-100 border-red-500 text-red-800',
                gray: 'bg-gray-100 border-gray-500 text-gray-800',
            };

            // Component to display individual result cards
            const ResultCard = ({ icon, title, score, interpretation }) => (
                <div className={`p-6 rounded-xl shadow-md border-l-4 ${colorClasses[interpretation.color]}`}>
                    <div className="flex items-start">
                        <div className="flex-shrink-0">{icon}</div>
                        <div className="ml-4">
                            <h3 className="text-xl font-bold">{title}</h3>
                            <p className="mt-2 text-lg">Skor Anda: <span className="font-extrabold">{score}</span></p>
                            <div className={`mt-2 inline-block px-3 py-1 text-sm font-semibold rounded-full ${colorClasses[interpretation.color].replace('border-l-4', '').replace('100', '200')}`}>
                                Kategori: {interpretation.risk}
                            </div>
                            <p className="mt-3">{interpretation.text}</p>
                        </div>
                    </div>
                </div>
            );

            // --- Export Functions ---

            const generateMarkdownContent = () => {
                let mdContent = `# Hasil Asesmen Kesehatan Mental Mandiri\n\n`;
                mdContent += `Berikut adalah rangkuman hasil asesmen mandiri Anda.\n\n`;

                // SRQ-29
                mdContent += `## SRQ-29 (Skrining Masalah Kesehatan Jiwa)\n`;
                mdContent += `- **Skor Anda:** ${srqScore}\n`;
                mdContent += `- **Kategori:** ${interpretations.srq.risk}\n`;
                mdContent += `- **Interpretasi:** ${interpretations.srq.text}\n\n`;

                // PHQ-9
                mdContent += `## PHQ-9 (Skrining Gejala Depresi)\n`;
                mdContent += `- **Skor Anda:** ${phqScore}\n`;
                mdContent += `- **Kategori:** ${interpretations.phq.risk}\n`;
                mdContent += `- **Interpretasi:** ${interpretations.phq.text}\n\n`;

                // SDQ-25 Total Difficulties
                mdContent += `## SDQ-25 (Skor Total Kesulitan)\n`;
                mdContent += `- **Skor Anda:** ${sdqScores.total}\n`;
                mdContent += `- **Kategori:** ${interpretations.sdq.risk}\n`;
                mdContent += `- **Interpretasi:** ${interpretations.sdq.text}\n\n`;

                // SDQ-25 Prosocial Strength
                mdContent += `## SDQ-25 (Skor Kekuatan Prososial)\n`;
                mdContent += `- **Skor Anda:** ${sdqScores.prosocial}\n`;
                mdContent += `- **Kategori:** ${interpretations.sdqProsocial.risk}\n`;
                mdContent += `- **Interpretasi:** ${interpretations.sdqProsocial.text}\n\n`;

                mdContent += `### Langkah Selanjutnya\n`;
                mdContent += `Jika hasil asesmen menunjukkan adanya potensi masalah atau jika Anda merasa khawatir, sangat dianjurkan untuk berbicara dengan orang yang Anda percaya, seperti Guru BK, orang tua, atau wali. Mereka dapat membantu Anda mendapatkan dukungan yang tepat.\n\n`;
                mdContent += `--- \n`;
                mdContent += `&copy; ${new Date().getFullYear()} Asesmen Kesehatan Mental. Dibuat untuk mendukung kesejahteraan siswa.`;

                return mdContent;
            };

            const handleExportPdf = () => {
                const element = document.getElementById('results-content'); // Target the results content
                if (typeof window.html2pdf === 'undefined') {
                    console.error("html2pdf.js is not loaded. Please ensure the script is included.");
                    // You might want to show a user-friendly message here
                    return;
                }
                const opt = {
                    margin: 1,
                    filename: 'hasil_asesmen_kesehatan_mental.pdf',
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { scale: 2, useCORS: true },
                    jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
                };
                window.html2pdf().set(opt).from(element).save();
            };

            const handleExportMarkdown = () => {
                const markdownContent = generateMarkdownContent();
                const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'hasil_asesmen_kesehatan_mental.md';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            };

            const handlePrint = () => {
                window.print();
            };

            return (
                <div className="text-center">
                    <FileText className="mx-auto h-20 w-20 text-blue-500" />
                    <h1 className="mt-4 text-3xl md:text-4xl font-bold text-gray-800">Hasil Asesmen Anda</h1>
                    <p className="mt-2 text-gray-600">Berikut adalah rangkuman hasil asesmen mandiri Anda.</p>
                    
                    {/* Content to be exported/printed */}
                    <div id="results-content" className="mt-8 text-left space-y-6 max-w-4xl mx-auto p-4 bg-white rounded-lg shadow-inner">
                        <ResultCard
                            icon={<HeartPulse className={`h-8 w-8 text-red-500`} />}
                            title="SRQ-29 (Skrining Masalah Kesehatan Jiwa)"
                            score={srqScore}
                            interpretation={interpretations.srq}
                        />
                        <ResultCard
                            icon={<BrainCircuit className={`h-8 w-8 text-blue-500`} />}
                            title="PHQ-9 (Skrining Gejala Depresi)"
                            score={phqScore}
                            interpretation={interpretations.phq}
                        />
                        <ResultCard
                            icon={<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-yellow-500"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>}
                            title="SDQ-25 (Skor Total Kesulitan)"
                            score={sdqScores.total}
                            interpretation={interpretations.sdq}
                        />
                           <ResultCard
                            icon={<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500"><path d="M7 10v12"/><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a2 2 0 0 1 3 1.88Z"/></svg>}
                            title="SDQ-25 (Skor Kekuatan Prososial)"
                            score={sdqScores.prosocial}
                            interpretation={interpretations.sdqProsocial}
                        />
                    </div>

                    <div className="mt-10 p-4 bg-yellow-50 border-l-4 border-yellow-500 text-yellow-800 rounded-r-lg max-w-4xl mx-auto">
                        <p className="font-semibold">Langkah Selanjutnya</p>
                        <p className="text-sm mt-1">Jika hasil asesmen menunjukkan adanya potensi masalah atau jika Anda merasa khawatir, sangat dianjurkan untuk berbicara dengan orang yang Anda percaya, seperti Guru BK, orang tua, atau wali. Mereka dapat membantu Anda mendapatkan dukungan yang tepat.</p>
                    </div>

                    {/* Export and Print Buttons */}
                    <div className="mt-8 flex flex-wrap justify-center gap-4">
                        <button
                            onClick={handleExportPdf}
                            className="inline-flex items-center px-6 py-3 bg-red-600 text-white font-bold rounded-full shadow-lg hover:bg-red-700 transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-300"
                        >
                            <FileDown className="mr-2 h-5 w-5" /> Cetak PDF
                        </button>
                        <button
                            onClick={handleExportMarkdown}
                            className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-bold rounded-full shadow-lg hover:bg-purple-700 transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300"
                        >
                            <FileTextIcon className="mr-2 h-5 w-5" /> Export Markdown
                        </button>
                        <button
                            onClick={handlePrint}
                            className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-bold rounded-full shadow-lg hover:bg-green-700 transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300"
                        >
                            <Printer className="mr-2 h-5 w-5" /> Cetak Printer
                        </button>
                    </div>

                    <button
                        onClick={onRestart}
                        className="mt-8 inline-flex items-center justify-center px-8 py-4 bg-gray-600 text-white font-bold rounded-full shadow-lg hover:bg-gray-700 transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-300"
                    >
                        <Repeat className="mr-2 h-5 w-5" /> Ulangi Asesmen
                    </button>
                </div>
            );
        };

        // --- Komponen Utama Aplikasi ---

        function App() {
            // State to manage the current step of the assessment
            const [step, setStep] = useState(0); // 0: Welcome, 1: SRQ, 2: PHQ, 3: SDQ, 4: Results
            // State to store answers for each questionnaire
            const [answers, setAnswers] = useState({
                srq: {},
                phq: {},
                sdq: {},
            });

            // Firebase states
            const [db, setDb] = useState(null);
            const [auth, setAuth] = useState(null);
            const [userId, setUserId] = useState(null);
            const [isAuthReady, setIsAuthReady] = useState(false);

            // Initialize Firebase and set up auth listener
            useEffect(() => {
                try {
                    const appId = typeof window.__app_id !== 'undefined' ? window.__app_id : 'default-app-id';
                    const firebaseConfig = JSON.parse(typeof window.__firebase_config !== 'undefined' ? window.__firebase_config : '{}');

                    const app = initializeApp(firebaseConfig);
                    const firestoreDb = getFirestore(app);
                    const firebaseAuth = getAuth(app);

                    setDb(firestoreDb);
                    setAuth(firebaseAuth);

                    // Listen for auth state changes
                    const unsubscribe = onAuthStateChanged(firebaseAuth, async (user) => {
                        if (user) {
                            setUserId(user.uid);
                        } else {
                            // Sign in anonymously if no user is logged in
                            try {
                                if (typeof window.__initial_auth_token !== 'undefined' && window.__initial_auth_token) {
                                    await signInWithCustomToken(firebaseAuth, window.__initial_auth_token);
                                } else {
                                    await signInAnonymously(firebaseAuth);
                                }
                                setUserId(firebaseAuth.currentUser?.uid || crypto.randomUUID());
                            } catch (error) {
                                console.error("Firebase Anonymous Auth Error:", error);
                                setUserId(crypto.randomUUID()); // Fallback to a random ID
                            }
                        }
                        setIsAuthReady(true);
                    });

                    return () => unsubscribe(); // Cleanup auth listener
                } catch (error) {
                    console.error("Firebase Initialization Error:", error);
                    setIsAuthReady(true); // Mark ready even if error, to unblock UI
                    setUserId(crypto.randomUUID()); // Fallback to a random ID
                }
            }, []);

            // Function to save answers to Firestore
            const saveAnswersToFirestore = async (currentAnswers) => {
                if (!db || !userId) {
                    console.warn("Firestore not initialized or user ID not available. Cannot save answers.");
                    return;
                }

                try {
                    const appId = typeof window.__app_id !== 'undefined' ? window.__app_id : 'default-app-id';
                    const assessmentDocRef = doc(db, `artifacts/${appId}/users/${userId}/assessments`, `assessment_${Date.now()}`);
                    await setDoc(assessmentDocRef, {
                        userId: userId,
                        answers: currentAnswers,
                        timestamp: serverTimestamp(),
                        srqScore: Object.values(currentAnswers.srq).filter(ans => ans === 1).length,
                        phqScore: Object.values(currentAnswers.phq).reduce((sum, val) => sum + val, 0),
                        // SDQ scores calculation for saving
                        sdqScores: (() => {
                            const scores = { emotional: 0, conduct: 0, hyperactivity: 0, peer: 0, prosocial: 0, total: 0 };
                            sdqQuestions.forEach(q => {
                                let value = currentAnswers.sdq[q.id] || 0;
                                if (q.reverse) {
                                    if (value === 0) value = 2;
                                    else if (value === 2) value = 0;
                                }
                                scores[q.scale] += value;
                            });
                            scores.total = scores.emotional + scores.conduct + scores.hyperactivity + scores.peer;
                            return scores;
                        })(),
                    });
                    console.log("Answers saved to Firestore successfully!");
                } catch (error) {
                    console.error("Error saving answers to Firestore:", error);
                }
            };

            // Define steps for the progress bar (not fully implemented in UI, but useful for logic)
            const steps = [
                { name: 'Selamat Datang', icon: <BrainCircuit/> },
                { name: 'SRQ-29', icon: <HeartPulse/> },
                { name: 'PHQ-9', icon: <BrainCircuit/> },
                { name: 'SDQ-25', icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg> },
                { name: 'Hasil', icon: <FileText/> }
            ];

            // Handler for changing answers for a specific questionnaire
            const handleAnswerChange = (form, id, value) => {
                setAnswers(prev => ({
                    ...prev,
                    [form]: {
                        ...prev[form],
                        [id]: value,
                    },
                }));
            };

            // Handler for navigating to the next step
            const handleNext = async () => {
                if (step < 4) {
                    // If moving from the last questionnaire to results, save answers
                    if (step === 3) {
                        await saveAnswersToFirestore(answers);
                    }
                    setStep(s => s + 1);
                    window.scrollTo(0, 0); // Scroll to top on step change
                }
            };

            // Handler for navigating to the previous step
            const handleBack = () => {
                if (step > 0) {
                    setStep(s => s - 1);
                    window.scrollTo(0, 0); // Scroll to top on step change
                }
            };
            
            // Handler for restarting the assessment
            const handleRestart = () => {
                setAnswers({ srq: {}, phq: {}, sdq: {} }); // Reset all answers
                setStep(0); // Go back to welcome screen
                window.scrollTo(0, 0); // Scroll to top
            };

            // Check if all questions in the current step are answered
            const isCurrentStepComplete = () => {
                switch (step) {
                    case 1: return Object.keys(answers.srq).length === srqQuestions.length;
                    case 2: return Object.keys(answers.phq).length === phqQuestions.length;
                    case 3: return Object.keys(answers.sdq).length === sdqQuestions.length;
                    default: return true; // Welcome and results screens are always "complete"
                }
            };

            // Render content based on the current step
            const renderStepContent = () => {
                if (!isAuthReady) {
                    return (
                        <div className="text-center py-20">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                            <p className="mt-4 text-gray-600">Memuat aplikasi...</p>
                        </div>
                    );
                }
                switch (step) {
                    case 0: return <WelcomeScreen onStart={handleNext} />;
                    case 1: return <QuestionnaireStep
                        title="SRQ-29 (Self Reporting Questionnaire)"
                        description="Pilih 'Ya' jika Anda mengalami hal ini dalam 30 hari terakhir. Jika tidak, pilih 'Tidak'."
                        questions={srqQuestions}
                        options={[{ label: 'Ya', value: 1 }, { label: 'Tidak', value: 0 }]}
                        answers={answers.srq}
                        onAnswerChange={(id, val) => handleAnswerChange('srq', id, val)}
                    />;
                    case 2: return <QuestionnaireStep
                        title="PHQ-9 (Patient Health Questionnaire)"
                        description="Seberapa sering Anda terganggu oleh masalah-masalah berikut selama 2 minggu terakhir?"
                        questions={phqQuestions}
                        options={phqOptions}
                        answers={answers.phq}
                        onAnswerChange={(id, val) => handleAnswerChange('phq', id, val)}
                    />;
                    case 3: return <QuestionnaireStep
                        title="SDQ-25 (Strengths and Difficulties Questionnaire)"
                        description="Untuk setiap pernyataan, tandai pilihan yang paling sesuai dengan diri Anda selama 6 bulan terakhir."
                        questions={sdqQuestions}
                        options={sdqOptions}
                        answers={answers.sdq}
                        onAnswerChange={(id, val) => handleAnswerChange('sdq', id, val)}
                    />;
                    case 4: return <ResultsScreen answers={answers} onRestart={handleRestart} />;
                    default: return <WelcomeScreen onStart={handleNext} />;
                }
            };
            
            // Calculate progress for the progress bar
            const progress = step === 0 ? 0 : (step / (steps.length -1)) * 100;

            return (
                <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-100 font-sans text-gray-900">
                    <div className="container mx-auto px-4 py-8 md:py-12">
                        
                        {/* User ID display */}
                        {userId && isAuthReady && (
                            <div className="flex items-center justify-center mb-4 text-sm text-gray-600">
                                <Database className="h-4 w-4 mr-2 text-blue-500" />
                                ID Pengguna: <span className="font-mono ml-1 px-2 py-1 bg-gray-100 rounded-md">{userId}</span>
                            </div>
                        )}

                        {/* Progress bar and step indicator */}
                        {step > 0 && step < 4 && (
                            <div className="mb-8">
                                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div className="bg-blue-600 h-2.5 rounded-full transition-all duration-500" style={{width: `${progress}%`}}></div>
                                </div>
                                <p className="text-center text-sm text-gray-600 mt-2">{steps[step].name} - Langkah {step} dari 3</p>
                            </div>
                        )}

                        {/* Main content area */}
                        <main className="bg-white/70 backdrop-blur-xl p-6 sm:p-8 md:p-12 rounded-2xl shadow-lg border border-gray-200/50">
                            {renderStepContent()}
                        </main>

                        {/* Navigation buttons */}
                        {step > 0 && step < 4 && (
                            <div className="mt-8 flex justify-between items-center">
                                <button
                                    onClick={handleBack}
                                    disabled={step === 1} // Disable back button on the first questionnaire step
                                    className="inline-flex items-center px-6 py-3 bg-white border border-gray-300 text-gray-700 font-bold rounded-full shadow-sm hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    <ChevronsLeft className="mr-2 h-5 w-5" />
                                    Kembali
                                </button>
                                <button
                                    onClick={handleNext}
                                    disabled={!isCurrentStepComplete()} // Disable next button if current step is not complete
                                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-bold rounded-full shadow-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-4 focus:ring-blue-300"
                                >
                                    {step === 3 ? 'Lihat Hasil' : 'Lanjut'} {/* Change button text on the last questionnaire step */}
                                    <ChevronsRight className="ml-2 h-5 w-5" />
                                </button>
                            </div>
                        )}
                    </div>
                       {/* Footer */}
                       <footer className="text-center py-6 text-sm text-gray-500">
                           <p>&copy; {new Date().getFullYear()} Asesmen Kesehatan Mental. Dibuat untuk mendukung kesejahteraan siswa.</p>
                       </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
