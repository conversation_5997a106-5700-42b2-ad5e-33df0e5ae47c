/**
 * Form Configurations for All Assessment Types
 * Complete configuration for SRQ-20, DASS-42, GSE, MSCS, MHKQ
 */

const FormConfigs = {
    // SRQ-20 Configuration
    SRQ20: {
        code: 'SRQ20',
        name: 'Self Reporting Questionnaire (SRQ-20)',
        description: 'Skrining gangguan mental umum dengan 20 pertanyaan sederhana',
        category: 'mental_health',
        time_limit: 15,
        instructions: '<PERSON><PERSON><PERSON>h pertanyaan berikut berdasarkan apa yang Anda rasakan dalam 30 hari terakhir. Pilih "Ya" jika Anda mengalami gejala tersebut, atau "Tidak" jika tidak mengalaminya.',
        scoring_rules: {
            type: 'binary_sum',
            max_score: 20,
            thresholds: {
                normal: { min: 0, max: 7 },
                concern: { min: 8, max: 12 },
                high_risk: { min: 13, max: 20 }
            }
        },
        questions: [
            "Apakah Anda sering merasa sakit kepala?",
            "Apakah nafsu makan Anda berkurang?",
            "Apakah tidur Anda tidak nyenyak?",
            "Apakah Anda mudah takut?",
            "Apakah tangan Anda gemetar?",
            "Apakah Anda merasa tegang, cemas atau khawatir?",
            "Apakah pencernaan Anda terganggu?",
            "Apakah Anda sulit berpikir jernih?",
            "Apakah Anda merasa tidak bahagia?",
            "Apakah Anda lebih sering menangis?",
            "Apakah Anda sulit menikmati kegiatan sehari-hari?",
            "Apakah Anda sulit mengambil keputusan?",
            "Apakah pekerjaan sehari-hari Anda terganggu?",
            "Apakah Anda tidak mampu berperan dalam hidup?",
            "Apakah Anda kehilangan minat terhadap berbagai hal?",
            "Apakah Anda merasa tidak berharga?",
            "Apakah Anda mempunyai pikiran untuk mengakhiri hidup?",
            "Apakah Anda merasa lelah sepanjang waktu?",
            "Apakah Anda merasa tidak enak di perut?",
            "Apakah Anda mudah lelah?"
        ]
    },

    // DASS-42 Configuration
    DASS42: {
        code: 'DASS42',
        name: 'Depression Anxiety Stress Scale (DASS-42)',
        description: 'Mengukur tingkat depresi, kecemasan, dan stres dengan 42 pertanyaan',
        category: 'psychological',
        time_limit: 25,
        instructions: 'Bacalah setiap pernyataan dan pilih angka yang paling menggambarkan keadaan Anda selama satu minggu terakhir. 0 = Tidak sesuai sama sekali, 1 = Kadang-kadang, 2 = Lumayan sering, 3 = Sering sekali.',
        scoring_rules: {
            type: 'dass_scale',
            max_score: 126,
            subscales: {
                depression: { items: [3, 5, 10, 13, 16, 17, 21, 24, 26, 31, 34, 37, 38, 42], multiplier: 2 },
                anxiety: { items: [2, 4, 7, 9, 15, 19, 20, 23, 25, 28, 30, 36, 40, 41], multiplier: 2 },
                stress: { items: [1, 6, 8, 11, 12, 14, 18, 22, 27, 29, 32, 33, 35, 39], multiplier: 2 }
            },
            thresholds: {
                depression: { normal: [0, 9], mild: [10, 13], moderate: [14, 20], severe: [21, 27], extremely_severe: [28, 42] },
                anxiety: { normal: [0, 7], mild: [8, 9], moderate: [10, 14], severe: [15, 19], extremely_severe: [20, 42] },
                stress: { normal: [0, 14], mild: [15, 18], moderate: [19, 25], severe: [26, 33], extremely_severe: [34, 42] }
            }
        },
        questions: [
            "Saya merasa sulit untuk tenang",
            "Saya merasa mulut saya kering",
            "Saya tidak dapat merasakan perasaan positif sama sekali",
            "Saya mengalami kesulitan bernapas (misalnya, napas cepat, kehabisan napas tanpa melakukan aktivitas fisik)",
            "Saya merasa sulit untuk memulai melakukan sesuatu",
            "Saya cenderung bereaksi berlebihan terhadap situasi",
            "Saya mengalami gemetar (misalnya, di tangan)",
            "Saya merasa bahwa saya menggunakan banyak energi mental",
            "Saya khawatir dengan situasi di mana saya mungkin panik dan mempermalukan diri sendiri",
            "Saya merasa bahwa saya tidak ada hal yang dapat ditunggu",
            "Saya mendapati diri saya menjadi gelisah",
            "Saya merasa sulit untuk rileks",
            "Saya merasa sedih dan tertekan",
            "Saya tidak toleran terhadap hal apapun yang menghalangi saya untuk melanjutkan apa yang sedang saya lakukan",
            "Saya merasa hampir panik",
            "Saya tidak dapat merasa antusias tentang apapun",
            "Saya merasa bahwa saya tidak berharga sebagai seseorang",
            "Saya merasa bahwa saya agak sensitif",
            "Saya menyadari kerja jantung saya walaupun saya tidak melakukan aktivitas fisik (misalnya, merasa detak jantung meningkat atau melewatkan detak jantung)",
            "Saya merasa takut tanpa alasan yang jelas",
            "Saya merasa bahwa hidup tidak berarti",
            "Saya merasa sulit untuk tenang",
            "Saya merasa sulit menelan",
            "Saya tidak dapat menikmati hal-hal yang saya lakukan",
            "Saya menyadari aktivitas jantung saya (misalnya, detak jantung meningkat atau melewatkan detak jantung)",
            "Saya merasa putus asa dan sedih",
            "Saya merasa bahwa saya mudah marah",
            "Saya merasa bahwa saya dekat dengan kepanikan",
            "Saya merasa sulit untuk tenang setelah sesuatu yang mengganggu saya",
            "Saya takut bahwa saya akan 'terhambat' oleh tugas-tugas sepele yang tidak biasa saya lakukan",
            "Saya merasa bahwa saya tidak ada yang dapat ditunggu",
            "Saya merasa sulit untuk sabar",
            "Saya merasa sedih dan tertekan",
            "Saya tidak toleran terhadap gangguan apapun ketika saya sedang melakukan sesuatu",
            "Saya merasa hampir panik",
            "Saya tidak dapat merasa bersemangat tentang apapun",
            "Saya merasa bahwa saya tidak berharga sebagai seseorang",
            "Saya merasa bahwa saya agak sensitif",
            "Saya menyadari kerja jantung saya walaupun saya tidak melakukan aktivitas fisik",
            "Saya merasa takut tanpa alasan yang jelas",
            "Saya merasa bahwa hidup tidak berarti",
            "Saya merasa sulit untuk tenang"
        ]
    },

    // GSE Configuration
    GSE: {
        code: 'GSE',
        name: 'General Self-Efficacy Scale',
        description: 'Mengukur keyakinan diri dalam menghadapi berbagai situasi sulit',
        category: 'self_efficacy',
        time_limit: 10,
        instructions: 'Untuk setiap pernyataan, pilih angka yang paling sesuai dengan keyakinan Anda. 1 = Tidak benar sama sekali, 2 = Hampir tidak benar, 3 = Agak benar, 4 = Benar sekali.',
        scoring_rules: {
            type: 'likert_sum',
            min_score: 10,
            max_score: 40,
            scale: { min: 1, max: 4 },
            thresholds: {
                low: { min: 10, max: 25 },
                moderate: { min: 26, max: 32 },
                high: { min: 33, max: 40 }
            }
        },
        questions: [
            "Saya dapat menyelesaikan masalah sulit jika saya berusaha keras",
            "Jika seseorang menentang saya, saya dapat menemukan cara untuk mendapatkan apa yang saya inginkan",
            "Mudah bagi saya untuk tetap pada tujuan saya dan mencapai target saya",
            "Saya yakin bahwa saya dapat menangani kejadian tak terduga secara efektif",
            "Berkat kecerdikan saya, saya tahu bagaimana menangani situasi yang tidak terduga",
            "Saya dapat menyelesaikan sebagian besar masalah jika saya berusaha keras",
            "Saya dapat tetap tenang ketika menghadapi kesulitan karena saya dapat mengandalkan kemampuan mengatasi masalah saya",
            "Ketika saya dihadapkan dengan masalah, saya biasanya dapat menemukan beberapa solusi",
            "Jika saya dalam kesulitan, saya biasanya dapat memikirkan solusi",
            "Saya biasanya dapat menangani apa pun yang terjadi pada saya"
        ]
    },

    // MSCS Configuration
    MSCS: {
        code: 'MSCS',
        name: 'Multidimensional Scale of Cultural Sensitivity',
        description: 'Mengukur sensitivitas budaya dalam konteks multikultural',
        category: 'cultural_sensitivity',
        time_limit: 15,
        instructions: 'Untuk setiap pernyataan, pilih angka yang menggambarkan seberapa sesuai dengan kondisi Anda. 1 = Sangat tidak setuju, 7 = Sangat setuju.',
        scoring_rules: {
            type: 'likert_sum',
            min_score: 25,
            max_score: 175,
            scale: { min: 1, max: 7 },
            thresholds: {
                low: { min: 25, max: 75 },
                moderate: { min: 76, max: 125 },
                high: { min: 126, max: 175 }
            }
        },
        questions: [
            "Saya merasa nyaman berinteraksi dengan orang dari budaya yang berbeda",
            "Saya berusaha memahami perspektif orang dari latar belakang budaya yang berbeda",
            "Saya menghargai keragaman budaya di lingkungan saya",
            "Saya dapat beradaptasi dengan norma budaya yang berbeda",
            "Saya tertarik mempelajari budaya lain",
            "Saya merasa mudah berkomunikasi dengan orang dari budaya berbeda",
            "Saya menghormati tradisi dan kepercayaan budaya lain",
            "Saya dapat bekerja sama dengan orang dari berbagai latar belakang budaya",
            "Saya merasa bahwa keragaman budaya memperkaya pengalaman hidup",
            "Saya berusaha menghindari stereotip budaya",
            "Saya dapat menyesuaikan gaya komunikasi saya dengan budaya yang berbeda",
            "Saya merasa nyaman dalam situasi multikultural",
            "Saya berusaha belajar dari perbedaan budaya",
            "Saya dapat mengatasi konflik yang muncul dari perbedaan budaya",
            "Saya menghargai kontribusi dari berbagai budaya",
            "Saya merasa bahwa semua budaya memiliki nilai yang sama",
            "Saya dapat beradaptasi dengan lingkungan budaya yang baru",
            "Saya berusaha membangun hubungan lintas budaya",
            "Saya merasa bahwa perbedaan budaya adalah kekuatan",
            "Saya dapat memahami perspektif minoritas budaya",
            "Saya berusaha menghilangkan bias budaya dalam diri saya",
            "Saya merasa nyaman menjadi minoritas dalam kelompok budaya",
            "Saya dapat menghargai cara pandang yang berbeda dari budaya lain",
            "Saya berusaha mempromosikan inklusi budaya",
            "Saya merasa bahwa keragaman budaya penting untuk masyarakat"
        ]
    },

    // MHKQ Configuration
    MHKQ: {
        code: 'MHKQ',
        name: 'Mental Health Knowledge Questionnaire',
        description: 'Mengukur pengetahuan tentang kesehatan mental dan gangguan jiwa',
        category: 'knowledge',
        time_limit: 20,
        instructions: 'Pilih "Benar" atau "Salah" untuk setiap pernyataan berdasarkan pengetahuan Anda tentang kesehatan jiwa.',
        scoring_rules: {
            type: 'knowledge_sum',
            max_score: 20,
            correct_answers: {
                1: 'benar', 2: 'salah', 3: 'benar', 4: 'benar', 5: 'salah',
                6: 'benar', 7: 'salah', 8: 'benar', 9: 'benar', 10: 'salah',
                11: 'benar', 12: 'salah', 13: 'benar', 14: 'benar', 15: 'salah',
                16: 'benar', 17: 'salah', 18: 'benar', 19: 'benar', 20: 'salah'
            },
            thresholds: {
                low: { min: 0, max: 10 },
                moderate: { min: 11, max: 15 },
                high: { min: 16, max: 20 }
            }
        },
        questions: [
            "Gangguan mental dapat dialami oleh siapa saja, tanpa memandang usia, jenis kelamin, atau status sosial",
            "Orang dengan gangguan mental selalu berbahaya dan tidak dapat dipercaya",
            "Stres yang berkepanjangan dapat memicu gangguan mental",
            "Dukungan keluarga dan teman sangat penting dalam pemulihan gangguan mental",
            "Gangguan mental adalah tanda kelemahan karakter seseorang",
            "Pengobatan dan terapi dapat membantu orang dengan gangguan mental",
            "Orang dengan gangguan mental tidak dapat bekerja atau berfungsi normal",
            "Depresi adalah kondisi medis yang nyata, bukan hanya perasaan sedih biasa",
            "Gangguan kecemasan dapat diobati dengan terapi dan/atau obat-obatan",
            "Orang dengan gangguan mental sebaiknya diisolasi dari masyarakat",
            "Kesehatan mental sama pentingnya dengan kesehatan fisik",
            "Gangguan mental hanya dialami oleh orang dewasa",
            "Olahraga dan aktivitas fisik dapat membantu meningkatkan kesehatan mental",
            "Stigma terhadap gangguan mental dapat memperburuk kondisi penderita",
            "Gangguan mental tidak dapat dicegah",
            "Berbicara dengan konselor atau psikolog dapat membantu mengatasi masalah mental",
            "Obat-obatan psikiatri selalu membuat orang menjadi 'zombie'",
            "Gangguan bipolar melibatkan perubahan suasana hati yang ekstrem",
            "Meditasi dan teknik relaksasi dapat membantu mengurangi stres",
            "Orang dengan skizofrenia memiliki kepribadian ganda"
        ]
    },

    // PDD Configuration (Pervasive Developmental Disorder Screening)
    PDD: {
        code: 'PDD',
        name: 'Pervasive Developmental Disorder Screening',
        description: 'Skrining untuk mendeteksi gangguan perkembangan pervasif pada anak dan remaja',
        category: 'developmental',
        time_limit: 25,
        instructions: 'Jawablah setiap pertanyaan berdasarkan pengamatan terhadap perilaku anak/remaja dalam 6 bulan terakhir. Pilih "Ya" jika perilaku tersebut sering terlihat, atau "Tidak" jika jarang atau tidak pernah terlihat.',
        scoring_rules: {
            type: 'pdd_screening',
            max_score: 30,
            domains: {
                social_interaction: { items: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], weight: 1 },
                communication: { items: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20], weight: 1 },
                repetitive_behavior: { items: [21, 22, 23, 24, 25, 26, 27, 28, 29, 30], weight: 1 }
            },
            thresholds: {
                low_risk: { min: 0, max: 8 },
                moderate_risk: { min: 9, max: 15 },
                high_risk: { min: 16, max: 30 }
            }
        },
        questions: [
            // Social Interaction Domain (1-10)
            "Anak menghindari kontak mata atau kontak mata yang tidak wajar",
            "Anak kesulitan dalam bermain dengan teman sebaya",
            "Anak tidak menunjukkan minat untuk berbagi kegembiraan atau pencapaian dengan orang lain",
            "Anak tidak merespons ketika namanya dipanggil",
            "Anak kesulitan memahami isyarat sosial non-verbal (ekspresi wajah, bahasa tubuh)",
            "Anak tidak menunjukkan empati terhadap perasaan orang lain",
            "Anak lebih suka bermain sendiri daripada dengan orang lain",
            "Anak kesulitan memulai atau mempertahankan percakapan",
            "Anak tidak menunjukkan perilaku meniru atau imitasi sosial",
            "Anak kesulitan dalam permainan imajinatif atau pura-pura",

            // Communication Domain (11-20)
            "Anak mengalami keterlambatan atau tidak ada perkembangan bahasa lisan",
            "Anak menggunakan bahasa yang berulang-ulang (echolalia)",
            "Anak kesulitan memulai atau melanjutkan percakapan",
            "Anak menggunakan bahasa dengan cara yang aneh atau tidak biasa",
            "Anak kesulitan memahami humor, sarkasme, atau bahasa kiasan",
            "Anak tidak menggunakan gesture atau isyarat untuk berkomunikasi",
            "Anak berbicara dengan nada atau ritme yang tidak biasa",
            "Anak kesulitan memahami instruksi yang kompleks",
            "Anak tidak menunjukkan minat dalam bercerita atau berbagi pengalaman",
            "Anak kesulitan dalam komunikasi dua arah yang timbal balik",

            // Repetitive Behavior Domain (21-30)
            "Anak menunjukkan gerakan berulang (mengepakkan tangan, bergoyang, dll)",
            "Anak memiliki rutinitas atau ritual yang kaku dan harus diikuti",
            "Anak menunjukkan minat yang sangat terbatas dan intens pada topik tertentu",
            "Anak terganggu oleh perubahan kecil dalam rutinitas atau lingkungan",
            "Anak memiliki ketertarikan yang tidak biasa terhadap bagian-bagian objek",
            "Anak menunjukkan perilaku self-stimulatory (menyentuh, mencium, menjilat objek)",
            "Anak memiliki sensitivitas yang tidak biasa terhadap suara, cahaya, atau tekstur",
            "Anak menunjukkan perilaku yang merusak diri sendiri",
            "Anak memiliki pola tidur atau makan yang tidak biasa",
            "Anak menunjukkan reaksi yang ekstrem terhadap perubahan atau transisi"
        ]
    }
};

// Export for use in other modules
window.FormConfigs = FormConfigs;
