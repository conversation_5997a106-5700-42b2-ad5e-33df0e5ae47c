<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mindful Self-Care Scale (MSCS)</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Chart.js for results visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles to complement Tailwind */
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        /* Custom radio button styles for the new 4-option scale */
        .custom-radio input[type="radio"] {
            display: none;
        }
        .custom-radio label {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }
        .custom-radio input[type="radio"]:checked + label {
            background-color: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(102, 126, 234, 0.4);
        }
        /* Animation for screen transitions */
        .screen {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">

    <main class="w-full max-w-3xl mx-auto">
        <!-- Welcome Screen -->
        <section id="welcome-screen" class="screen card rounded-2xl shadow-2xl p-8 md:p-12 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Skala Perawatan Diri Penuh Kesadaran (MSCS)</h1>
            <p class="text-gray-600 mb-6">Berikut adalah 24 pernyataan mengenai perilaku perawatan diri Anda. Bacalah setiap pernyataan dengan saksama dan berilah jawaban yang paling sesuai dengan keadaan Anda sebenarnya.</p>
            <p class="text-xs text-gray-500 mb-8">Instrumen ini telah teruji dengan nilai reliabilitas (Cronbach's Alpha) sebesar 0,856.</p>
            <button id="start-btn" class="w-full md:w-auto bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                Mulai Penilaian
            </button>
        </section>

        <!-- Questionnaire Screen -->
        <section id="questionnaire-screen" class="screen card rounded-2xl shadow-2xl p-8 md:p-12 hidden">
            <!-- Progress Bar -->
            <div class="mb-8">
                <div class="flex justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Kemajuan</span>
                    <span id="progress-text" class="text-sm font-medium text-gray-700">Pertanyaan 1 dari 24</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div id="progress-bar" class="bg-indigo-500 h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                </div>
            </div>

            <!-- Question Area -->
            <div id="question-container" class="mb-8">
                <p id="question-text" class="text-xl md:text-2xl font-semibold text-gray-800 text-center mb-8 h-24 flex items-center justify-center"></p>
                <div id="options-container" class="grid grid-cols-2 md:grid-cols-4 gap-3 text-center">
                    <!-- Options will be injected by JS -->
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center">
                <button id="prev-btn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-200">
                    Kembali
                </button>
                <button id="next-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-indigo-300 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:shadow-none disabled:transform-none" disabled>
                    Lanjut
                </button>
            </div>
        </section>

        <!-- Results Screen -->
        <section id="results-screen" class="screen card rounded-2xl shadow-2xl p-8 md:p-12 hidden">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4 text-center">Hasil Penilaian Anda</h1>
            <p class="text-gray-600 mb-8 text-center">Berikut adalah rincian skor perawatan diri Anda. Gunakan ini sebagai panduan untuk memahami area mana yang sudah baik dan mana yang mungkin memerlukan perhatian lebih.</p>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <!-- Chart -->
                <div class="w-full h-80 lg:h-full">
                    <canvas id="results-chart"></canvas>
                </div>
                <!-- Scores and Interpretation -->
                <div id="scores-container" class="space-y-3">
                     <!-- Scores will be injected by JS -->
                </div>
            </div>
             <p id="total-score-text" class="text-center text-xl font-bold text-gray-800 mt-8"></p>

            <div class="text-center mt-8">
                <button id="restart-btn" class="w-full md:w-auto bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                    Ulangi Penilaian
                </button>
            </div>
        </section>

    </main>

    <script>
        // --- DATA & CONFIGURATION (Updated based on new questionnaire) ---

        const questions = [
            // Subscale: Manajemen Stres & Relaksasi
            { text: "Saya meluangkan waktu untuk relaksasi seperti meditasi atau pernapasan dalam.", subscale: "Manajemen Stres & Relaksasi" },
            { text: "Saya menjadwalkan waktu untuk istirahat dan menenangkan diri.", subscale: "Manajemen Stres & Relaksasi" },
            { text: "Saya melakukan aktivitas yang membantu saya merasa tenang.", subscale: "Manajemen Stres & Relaksasi" },
            { text: "Saya mencoba mengelola stres dengan cara yang sehat.", subscale: "Manajemen Stres & Relaksasi" },
            // Subscale: Perawatan Fisik
            { text: "Saya menjaga kebersihan diri seperti mandi dan menyikat gigi secara teratur.", subscale: "Perawatan Fisik" },
            { text: "Saya makan makanan bergizi untuk menjaga kesehatan saya.", subscale: "Perawatan Fisik" },
            { text: "Saya tidur dengan cukup untuk memulihkan tubuh dan pikiran.", subscale: "Perawatan Fisik" },
            { text: "Saya melakukan aktivitas fisik atau olahraga secara rutin.", subscale: "Perawatan Fisik" },
            // Subscale: Aktivitas Menyenangkan
            { text: "Saya menyisihkan waktu untuk melakukan hobi atau aktivitas yang saya nikmati.", subscale: "Aktivitas Menyenangkan" },
            { text: "Saya memberi hadiah kecil kepada diri saya sendiri sebagai bentuk penghargaan.", subscale: "Aktivitas Menyenangkan" },
            { text: "Saya memanjakan diri sendiri dengan cara yang positif.", subscale: "Aktivitas Menyenangkan" },
            { text: "Saya menyisihkan waktu hanya untuk diri saya sendiri.", subscale: "Aktivitas Menyenangkan" },
            // Subscale: Dukungan Sosial
            { text: "Saya berbicara dengan seseorang yang saya percaya ketika merasa stres.", subscale: "Dukungan Sosial" },
            { text: "Saya menjaga hubungan baik dengan teman atau keluarga.", subscale: "Dukungan Sosial" },
            { text: "Saya meluangkan waktu untuk bersosialisasi dengan orang-orang terdekat.", subscale: "Dukungan Sosial" },
            { text: "Saya merasa didukung oleh orang-orang di sekitar saya.", subscale: "Dukungan Sosial" },
            // Subscale: Mencari Bantuan
            { text: "Saya mencari bantuan profesional saat merasa tidak mampu mengatasi masalah.", subscale: "Mencari Bantuan" },
            { text: "Saya menggunakan layanan atau fasilitas kesehatan mental jika diperlukan.", subscale: "Mencari Bantuan" },
            { text: "Saya tahu ke mana harus pergi jika membutuhkan bantuan.", subscale: "Mencari Bantuan" },
            { text: "Saya memiliki sistem dukungan yang saya percaya.", subscale: "Mencari Bantuan" },
            // Subscale: Kesadaran Diri
            { text: "Saya menyadari tanda-tanda stres dalam diri saya.", subscale: "Kesadaran Diri" },
            { text: "Saya mengenali ketika saya membutuhkan waktu untuk istirahat.", subscale: "Kesadaran Diri" },
            { text: "Saya memahami apa yang membuat saya merasa tenang.", subscale: "Kesadaran Diri" },
            { text: "Saya mampu mengenali perasaan saya sendiri dengan baik.", subscale: "Kesadaran Diri" },
        ];

        const options = [
            { text: "Sangat Tdk Setuju", value: 1 },
            { text: "Tidak Setuju", value: 2 },
            { text: "Setuju", value: 3 },
            { text: "Sangat Setuju", value: 4 },
        ];
        
        const subscaleInfo = {
            "Manajemen Stres & Relaksasi": { color: "rgba(54, 162, 235, 0.8)", description: "Kemampuan mengelola stres dan meluangkan waktu untuk tenang." },
            "Perawatan Fisik": { color: "rgba(255, 99, 132, 0.8)", description: "Menjaga kesehatan tubuh melalui nutrisi, istirahat, dan kebersihan." },
            "Aktivitas Menyenangkan": { color: "rgba(255, 206, 86, 0.8)", description: "Meluangkan waktu untuk hobi dan penghargaan diri." },
            "Dukungan Sosial": { color: "rgba(75, 192, 192, 0.8)", description: "Menjaga hubungan positif dan merasa terhubung dengan orang lain." },
            "Mencari Bantuan": { color: "rgba(153, 102, 255, 0.8)", description: "Keterbukaan untuk mencari bantuan profesional atau dari sistem pendukung." },
            "Kesadaran Diri": { color: "rgba(255, 159, 64, 0.8)", description: "Memahami kondisi, emosi, dan kebutuhan diri sendiri." }
        };

        // --- STATE MANAGEMENT ---
        let currentQuestionIndex = 0;
        let answers = new Array(questions.length).fill(null);
        let chartInstance = null;

        // --- DOM ELEMENTS ---
        const welcomeScreen = document.getElementById('welcome-screen');
        const questionnaireScreen = document.getElementById('questionnaire-screen');
        const resultsScreen = document.getElementById('results-screen');
        const startBtn = document.getElementById('start-btn');
        const nextBtn = document.getElementById('next-btn');
        const prevBtn = document.getElementById('prev-btn');
        const restartBtn = document.getElementById('restart-btn');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const questionText = document.getElementById('question-text');
        const optionsContainer = document.getElementById('options-container');
        const scoresContainer = document.getElementById('scores-container');
        const totalScoreText = document.getElementById('total-score-text');
        const resultsChartCanvas = document.getElementById('results-chart').getContext('2d');

        // --- FUNCTIONS ---

        function startQuiz() {
            welcomeScreen.classList.add('hidden');
            questionnaireScreen.classList.remove('hidden');
            showQuestion(currentQuestionIndex);
        }

        function showQuestion(index) {
            const question = questions[index];
            questionText.textContent = question.text;

            optionsContainer.innerHTML = '';
            options.forEach(option => {
                const optionId = `q${index}_${option.value}`;
                const optionElement = document.createElement('div');
                optionElement.classList.add('custom-radio');
                optionElement.innerHTML = `
                    <input type="radio" id="${optionId}" name="question${index}" value="${option.value}">
                    <label for="${optionId}" class="block border border-gray-300 rounded-lg py-3 px-2 font-medium text-gray-700 hover:bg-gray-100">${option.text}</label>
                `;
                optionsContainer.appendChild(optionElement);
            });
            
            if (answers[index] !== null) {
                document.getElementById(`q${index}_${answers[index]}`).checked = true;
                nextBtn.disabled = false;
            } else {
                nextBtn.disabled = true;
            }

            updateProgress();
            prevBtn.style.visibility = index === 0 ? 'hidden' : 'visible';
            nextBtn.textContent = index === questions.length - 1 ? 'Lihat Hasil' : 'Lanjut';

            document.querySelectorAll(`input[name="question${index}"]`).forEach(radio => {
                radio.addEventListener('change', (e) => {
                    answers[index] = parseInt(e.target.value);
                    nextBtn.disabled = false;
                    setTimeout(() => handleNext(), 200);
                });
            });
        }

        function updateProgress() {
            const progressPercentage = ((currentQuestionIndex + 1) / questions.length) * 100;
            progressBar.style.width = `${progressPercentage}%`;
            progressText.textContent = `Pertanyaan ${currentQuestionIndex + 1} dari ${questions.length}`;
        }
        
        function handleNext() {
            if (currentQuestionIndex < questions.length - 1) {
                currentQuestionIndex++;
                showQuestion(currentQuestionIndex);
            } else {
                showResults();
            }
        }

        function handlePrev() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                showQuestion(currentQuestionIndex);
            }
        }

        function showResults() {
            questionnaireScreen.classList.add('hidden');
            resultsScreen.classList.remove('hidden');
            const results = calculateScores();
            displayScores(results);
            createChart(results);
        }
        
        function calculateScores() {
            const scores = {};
            const counts = {};
            
            Object.keys(subscaleInfo).forEach(subscale => {
                scores[subscale] = 0;
                counts[subscale] = 0;
            });

            questions.forEach((question, index) => {
                if (answers[index] !== null) {
                    scores[question.subscale] += answers[index];
                    counts[question.subscale]++;
                }
            });
            
            let totalScore = 0;
            let totalMaxScore = 0;
            Object.keys(scores).forEach(subscale => {
                totalScore += scores[subscale];
                totalMaxScore += counts[subscale] * options[options.length - 1].value;
            });

            return { subscaleScores: scores, totalScore, totalMaxScore };
        }
        
        function displayScores(results) {
            scoresContainer.innerHTML = '';
            Object.entries(results.subscaleScores).forEach(([subscale, score]) => {
                const info = subscaleInfo[subscale];
                const maxScorePerSubscale = (questions.filter(q => q.subscale === subscale).length) * 4;
                const scoreElement = document.createElement('div');
                scoreElement.className = 'p-3 rounded-lg border';
                scoreElement.style.borderColor = info.color;
                scoreElement.innerHTML = `
                    <div class="flex justify-between items-center">
                        <h3 class="font-semibold text-base" style="color: ${info.color};">${subscale}</h3>
                        <span class="font-bold text-lg" style="color: ${info.color};">${score} / ${maxScorePerSubscale}</span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">${info.description}</p>
                `;
                scoresContainer.appendChild(scoreElement);
            });
            
            totalScoreText.textContent = `Total Skor Anda: ${results.totalScore} dari ${results.totalMaxScore}`;
        }

        function createChart(results) {
            const labels = Object.keys(results.subscaleScores);
            const data = Object.values(results.subscaleScores);

            if (chartInstance) chartInstance.destroy();

            chartInstance = new Chart(resultsChartCanvas, {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Skor Perawatan Diri',
                        data: data,
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(102, 126, 234, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: { color: 'rgba(0, 0, 0, 0.1)' },
                            grid: { color: 'rgba(0, 0, 0, 0.1)' },
                            pointLabels: {
                                font: { size: 11, weight: 'bold' },
                                color: '#374151'
                            },
                            ticks: {
                                backdropColor: 'rgba(255, 255, 255, 0.9)',
                                color: '#6b7280',
                                stepSize: 4, // Each subscale has 4 questions, max score is 16. Step 4 is good.
                                beginAtZero: true
                            },
                            max: 16 // Max possible score for any subscale
                        }
                    },
                    plugins: { legend: { display: false } }
                }
            });
        }
        
        function resetQuiz() {
            currentQuestionIndex = 0;
            answers.fill(null);
            resultsScreen.classList.add('hidden');
            welcomeScreen.classList.remove('hidden');
            if (chartInstance) chartInstance.destroy();
        }

        // --- EVENT LISTENERS ---
        startBtn.addEventListener('click', startQuiz);
        nextBtn.addEventListener('click', handleNext);
        prevBtn.addEventListener('click', handlePrev);
        restartBtn.addEventListener('click', resetQuiz);

    </script>
</body>
</html>
