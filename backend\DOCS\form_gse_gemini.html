<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner General Self-Efficacy (GSE)</title>
    
    <!-- Tailwind CSS for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles to enhance the UI */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8; /* Light blue-gray background */
        }

        /* Custom radio button styling */
        .custom-radio input {
            display: none;
        }

        .custom-radio label {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }

        .custom-radio input:checked + label {
            background-color: #4f46e5; /* Indigo */
            color: white;
            border-color: #4f46e5;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3);
        }
        
        /* Smooth transition for result card */
        #result-card {
            transition: all 0.5s ease-in-out;
            max-height: 0;
            overflow: hidden;
            opacity: 0;
        }

        #result-card.visible {
            max-height: 1000px; /* Large enough to not clip content */
            opacity: 1;
            margin-top: 2rem;
        }
    </style>
</head>
<body class="antialiased text-gray-800">

    <!-- Main Container -->
    <div class="container mx-auto p-4 sm:p-6 md:p-8 max-w-4xl">

        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-900">Kuesioner General Self-Efficacy (GSE)</h1>
            <p class="mt-2 text-md text-gray-600">Ukur keyakinan pada kemampuan diri Anda untuk menghadapi tantangan hidup.</p>
        </header>

        <!-- Information Cards -->
        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <!-- Instructions Card -->
            <div class="bg-white p-6 rounded-xl shadow-md border border-gray-200">
                <h2 class="text-xl font-semibold mb-3 text-indigo-700">Petunjuk Pengisian</h2>
                <p class="text-gray-600 mb-4">Beri tanda pada salah satu pilihan yang paling sesuai dengan kondisi Anda untuk setiap pernyataan.</p>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><span class="font-bold text-indigo-600 w-6">4</span> = Benar sekali</li>
                    <li class="flex items-center"><span class="font-bold text-indigo-600 w-6">3</span> = Hampir benar</li>
                    <li class="flex items-center"><span class="font-bold text-indigo-600 w-6">2</span> = Hampir tidak benar</li>
                    <li class="flex items-center"><span class="font-bold text-indigo-600 w-6">1</span> = Tidak benar sama sekali</li>
                </ul>
            </div>
            <!-- About Card -->
            <div class="bg-white p-6 rounded-xl shadow-md border border-gray-200">
                <h2 class="text-xl font-semibold mb-3 text-indigo-700">Tentang GSE</h2>
                <p class="text-gray-600">
                    Skala Efikasi Diri Umum (GSE) adalah instrumen untuk menilai optimisme efikasi diri, yang merupakan keyakinan seseorang dalam kemampuannya untuk mengatasi tuntutan dan tantangan baru.
                </p>
            </div>
        </div>

        <!-- Questionnaire Form -->
        <form id="gse-form" class="bg-white p-6 sm:p-8 rounded-xl shadow-lg border border-gray-200">
            <div id="questionnaire-container" class="space-y-8">
                <!-- Questions will be dynamically inserted here by JavaScript -->
            </div>
            
            <!-- Error Message Placeholder -->
            <div id="error-message" class="hidden mt-6 text-center text-red-600 bg-red-100 p-3 rounded-lg">
                Harap jawab semua pertanyaan sebelum melihat hasil.
            </div>

            <!-- Submit Button -->
            <div class="mt-8 text-center">
                <button type="submit" class="bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-4 focus:ring-indigo-300 transition-all duration-300 transform hover:scale-105">
                    Lihat Hasil
                </button>
            </div>
        </form>

        <!-- Result Card (Initially Hidden) -->
        <div id="result-card" class="bg-white p-6 sm:p-8 rounded-xl shadow-lg border border-gray-200">
            <h2 class="text-2xl font-bold text-center mb-4">Hasil Penilaian Anda</h2>
            <div class="text-center">
                <p class="text-lg text-gray-600">Total Skor Anda:</p>
                <p id="total-score" class="text-6xl font-bold text-indigo-600 my-2">0</p>
                <div id="interpretation-badge" class="inline-block px-4 py-2 rounded-full font-semibold text-lg mt-2">
                    <p id="interpretation-text"></p>
                </div>
                <p id="interpretation-desc" class="mt-4 max-w-md mx-auto text-gray-600"></p>
            </div>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- DATA ---
            // The 10 questions for the GSE scale
            const questions = [
                { id: 1, text: "Saya bisa menghadapi masalah dengan tenang." },
                { id: 2, text: "Jika saya mengalami masalah psikologis, saya tahu cara mendapatkan bantuan." },
                { id: 3, text: "Saya yakin mampu menyelesaikan masalah yang sulit." },
                { id: 4, text: "Saya merasa yakin untuk menghadapi tantangan hidup." },
                { id: 5, text: "Saya bisa mencari pertolongan ketika merasa tertekan." },
                { id: 6, text: "Saya percaya pada kemampuan saya sendiri." },
                { id: 7, text: "Saya bisa mengambil keputusan dalam situasi yang tidak menentu." },
                { id: 8, text: "Saya merasa mampu mencari dukungan dari teman atau keluarga saat butuh." },
                { id: 9, text: "Saya yakin bisa menghadapi masa depan dengan baik." },
                { id: 10, text: "Saya mampu mengatasi tekanan dari lingkungan sekitar." }
            ];

            // The options for each question
            const options = [
                { value: 1, label: "Tidak benar sama sekali" },
                { value: 2, label: "Hampir tidak benar" },
                { value: 3, label: "Hampir benar" },
                { value: 4, label: "Benar sekali" }
            ];

            // --- RENDER QUESTIONS ---
            const questionnaireContainer = document.getElementById('questionnaire-container');
            
            // Function to dynamically create and append questions to the form
            function renderQuestions() {
                let questionsHTML = '';
                questions.forEach((q, index) => {
                    questionsHTML += `
                        <div id="question-card-${q.id}" class="p-5 rounded-lg border border-gray-200 transition-all duration-300">
                            <p class="font-semibold text-lg text-gray-800">${index + 1}. ${q.text}</p>
                            <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 custom-radio">
                                ${options.map(opt => `
                                    <div>
                                        <input type="radio" id="q${q.id}_opt${opt.value}" name="question_${q.id}" value="${opt.value}" required>
                                        <label for="q${q.id}_opt${opt.value}" class="block text-center p-3 rounded-lg border-2 border-gray-200 hover:border-indigo-400 hover:bg-indigo-50">
                                            <span class="font-medium">${opt.label}</span>
                                        </label>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                });
                questionnaireContainer.innerHTML = questionsHTML;
            }

            // --- FORM HANDLING ---
            const form = document.getElementById('gse-form');
            const errorMessage = document.getElementById('error-message');

            form.addEventListener('submit', function (event) {
                event.preventDefault(); // Prevent default form submission

                let totalScore = 0;
                let allAnswered = true;
                const userAnswers = {}; // Object to hold answers for API integration

                // Reset styles for all question cards
                questions.forEach(q => {
                    document.getElementById(`question-card-${q.id}`).classList.remove('border-red-400', 'bg-red-50');
                });

                // Loop through each question to get the selected answer
                for (let i = 1; i <= questions.length; i++) {
                    const selectedOption = document.querySelector(`input[name="question_${i}"]:checked`);
                    
                    if (selectedOption) {
                        const score = parseInt(selectedOption.value);
                        totalScore += score;
                        userAnswers[`q${i}`] = score;
                    } else {
                        allAnswered = false;
                        // Highlight unanswered question
                        document.getElementById(`question-card-${i}`).classList.add('border-red-400', 'bg-red-50');
                    }
                }

                // --- VALIDATION & RESULT DISPLAY ---
                if (allAnswered) {
                    errorMessage.classList.add('hidden');
                    displayResults(totalScore);
                    
                    // For Backend Integration:
                    // The 'userAnswers' object is ready to be sent.
                    console.log("Data siap dikirim ke API:", userAnswers);
                    // Example of how you might send it:
                    // fetch('/api/submit-gse', {
                    //     method: 'POST',
                    //     headers: { 'Content-Type': 'application/json' },
                    //     body: JSON.stringify(userAnswers)
                    // }).then(response => response.json()).then(data => console.log(data));

                } else {
                    errorMessage.classList.remove('hidden');
                    // Hide result card if it was visible
                    document.getElementById('result-card').classList.remove('visible');
                }
            });

            // --- HELPER FUNCTIONS ---
            
            // Function to determine interpretation based on score
            function getInterpretation(score) {
                if (score >= 35) return { level: "Sangat Tinggi", color: "bg-teal-100 text-teal-800", desc: "Anda memiliki keyakinan yang sangat kuat pada kemampuan Anda untuk mengatasi berbagai situasi sulit." };
                if (score >= 30) return { level: "Tinggi", color: "bg-green-100 text-green-800", desc: "Anda memiliki keyakinan yang baik pada kemampuan Anda untuk menghadapi tantangan." };
                if (score >= 25) return { level: "Cukup", color: "bg-yellow-100 text-yellow-800", desc: "Anda cukup yakin dengan kemampuan Anda, namun ada ruang untuk berkembang." };
                if (score >= 20) return { level: "Rendah", color: "bg-orange-100 text-orange-800", desc: "Anda mungkin sering merasa ragu dengan kemampuan Anda saat menghadapi kesulitan." };
                return { level: "Sangat Rendah", color: "bg-red-100 text-red-800", desc: "Anda memiliki tingkat keyakinan diri yang rendah dan mungkin memerlukan dukungan untuk meningkatkannya." };
            }

            // Function to display the final results
            function displayResults(score) {
                const interpretation = getInterpretation(score);
                const resultCard = document.getElementById('result-card');

                document.getElementById('total-score').textContent = score;
                document.getElementById('interpretation-text').textContent = interpretation.level;
                document.getElementById('interpretation-desc').textContent = interpretation.desc;
                
                const badge = document.getElementById('interpretation-badge');
                badge.className = 'inline-block px-4 py-2 rounded-full font-semibold text-lg mt-2 ' + interpretation.color;

                // Make the result card visible with a smooth animation
                resultCard.classList.add('visible');
                
                // Scroll to the result card for better user experience
                resultCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            // Initial render of the questions when the page loads
            renderQuestions();
        });
    </script>

</body>
</html>
