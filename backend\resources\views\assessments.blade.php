<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Pilih Assessment - SantriMental</title>

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="{{ asset('css/modern-dashboard.css') }}">

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .assessment-card {
            transition: all 0.3s ease;
        }
        
        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-4">
    
    <!-- Navigation Header -->
    <nav class="glass-card rounded-xl p-4 mb-6 max-w-6xl mx-auto">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="{{ route('dashboard') }}" class="text-white hover:text-purple-200 transition-colors mr-4">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                </a>
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">S</span>
                </div>
                <h1 class="text-xl font-bold text-white">SantriMental</h1>
            </div>
            <div class="text-purple-200 text-sm">
                Pilih Assessment
            </div>
        </div>
    </nav>

    <main class="max-w-6xl mx-auto">
        
        <!-- Header -->
        <div class="glass-card rounded-xl p-6 mb-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-4">Pilih Assessment Kesehatan Mental</h1>
            <p class="text-purple-200 text-lg max-w-3xl mx-auto">
                Pilih jenis assessment yang ingin Anda lakukan. Setiap assessment memiliki tujuan dan fokus yang berbeda 
                untuk membantu memahami kondisi kesehatan mental Anda.
            </p>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p class="text-purple-200">Memuat daftar assessment...</p>
        </div>

        <!-- Assessment Cards -->
        <div id="assessments-grid" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Assessment cards will be inserted here -->
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-purple-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">Tidak Ada Assessment</h3>
            <p class="text-purple-200">Belum ada assessment yang tersedia saat ini</p>
        </div>

    </main>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 glass-card p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <p id="toast-title" class="font-semibold text-white"></p>
                <p id="toast-message" class="text-purple-200 text-sm"></p>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const auth = window.auth;
            const Utils = window.Utils;

            // Check authentication
            if (!auth.isAuthenticated()) {
                window.location.href = '/';
                return;
            }

            const loadingState = document.getElementById('loading-state');
            const assessmentsGrid = document.getElementById('assessments-grid');
            const emptyState = document.getElementById('empty-state');

            try {
                // Load available assessments
                const response = await Utils.apiCall('/forms');
                
                if (response.success && response.data.length > 0) {
                    displayAssessments(response.data);
                } else {
                    showEmpty();
                }
            } catch (error) {
                console.error('Failed to load assessments:', error);
                Utils.showNotification('Gagal memuat daftar assessment', 'error');
                showEmpty();
            }

            function displayAssessments(assessments) {
                const assessmentIcons = {
                    'SRQ20': '🧠',
                    'GSE': '💪',
                    'MSCS': '🤝',
                    'MHKQ': '📚',
                    'DASS42': '📊'
                };

                const assessmentColors = {
                    'SRQ20': 'from-blue-500 to-purple-500',
                    'GSE': 'from-green-500 to-teal-500',
                    'MSCS': 'from-yellow-500 to-orange-500',
                    'MHKQ': 'from-pink-500 to-red-500',
                    'DASS42': 'from-indigo-500 to-blue-500'
                };

                const cardsHTML = assessments.map((assessment, index) => {
                    const icon = assessmentIcons[assessment.code] || '📋';
                    const colorClass = assessmentColors[assessment.code] || 'from-purple-500 to-pink-500';
                    
                    return `
                        <div class="assessment-card glass-card rounded-xl p-6 fade-in" style="animation-delay: ${index * 0.1}s;">
                            <div class="text-center mb-4">
                                <div class="w-16 h-16 bg-gradient-to-r ${colorClass} rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl">${icon}</span>
                                </div>
                                <h3 class="text-xl font-bold text-white mb-2">${assessment.name}</h3>
                                <p class="text-purple-200 text-sm leading-relaxed">${assessment.description}</p>
                            </div>
                            
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-purple-200 text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                    </svg>
                                    Waktu: ${assessment.time_limit || 'Tidak terbatas'} menit
                                </div>
                                <div class="flex items-center text-purple-200 text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                    Kategori: ${assessment.category.replace('_', ' ')}
                                </div>
                            </div>
                            
                            <button onclick="startAssessment('${assessment.code}')" 
                                    class="w-full bg-gradient-to-r ${colorClass} hover:opacity-90 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105">
                                Mulai Assessment
                            </button>
                        </div>
                    `;
                }).join('');

                assessmentsGrid.innerHTML = cardsHTML;
                showAssessments();
            }

            function showAssessments() {
                loadingState.classList.add('hidden');
                emptyState.classList.add('hidden');
                assessmentsGrid.classList.remove('hidden');
            }

            function showEmpty() {
                loadingState.classList.add('hidden');
                assessmentsGrid.classList.add('hidden');
                emptyState.classList.remove('hidden');
            }

            // Global function to start assessment
            window.startAssessment = (code) => {
                window.location.href = `/assessment/${code}`;
            };
        });
    </script>

    <!-- Modern JavaScript Framework -->
    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script src="{{ asset('js/modern-components.js') }}"></script>
    <script src="{{ asset('js/performance-optimizer.js') }}"></script>
    <script src="{{ asset('js/modern-dashboard.js') }}"></script>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
