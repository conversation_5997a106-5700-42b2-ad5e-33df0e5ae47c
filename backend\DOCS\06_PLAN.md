# SantriMental - Development Plan
## Mental Health Screening Platform for <PERSON><PERSON> (Islamic Boarding School Students)
### Development 

## Project Overview
Modern, responsive SRQ20 mental health screening dashboard with authentication and database integration capabilities. The platform is designed to provide a user-friendly interface for <PERSON><PERSON> to assess their mental health and receive recommendations. The system will be built using Laravel 10 for backend and management system. React Kit for Frontend. This project aims to create a comprehensive mental health screening platform for <PERSON><PERSON>, ensuring their well-being and support their mental health needs. The platform will be accessible via web and mobile devices with Webview support. 

The platform will be built with a modular architecture, allowing for easy expansion and customization. The system will be designed to be user-friendly, accessible, and secure. The platform will be integrated with a database to store user data and provide insights for mental health professionals. The system will be developed with a focus on scalability, maintainability, and security. The platform will be tested thoroughly to ensure its reliability and effectiveness. 

Simple UI/UX, Fresh Design, Modern, Sleek, and Clean. The platform will be designed to be accessible on various devices, including desktops, laptops, tablets, and mobile phones. The system will be developed with a focus on user experience, ensuring that the platform is easy to use and navigate.


## Features to Implement

### 1. Authentication System
- Login page with email/password
- User registration
- Google OAuth integration
- QR Code login option
- JWT token management
- Protected routes

### 2. Dashboard Components
- Welcome/Landing page
- SRQ20 questionnaire form
- Results visualization
- User profile management
- Assessment history
- Analytics dashboard

### 3. SRQ20 Form Features
- 20 standardized questions
- Yes/No responses (Ya=1, Tidak=0)
- Progress tracking
- Timer functionality
- Validation (score >6 = mental health concern)
- Results interpretation

### 4. Database Integration Ready
- REST API endpoints structure
- Data models for users and assessments
- Error handling
- Loading states
- Offline capability preparation

### 5. Modern UI/UX
- Vibrant gradient backgrounds
- Glassmorphism effects
- Smooth animations
- Responsive design
- Dark/light mode
- Accessibility features

## File Structure
```
/
├── index.html (Landing/Login page)
├── register.html (Registration page)
├── dashboard.html (Main dashboard)
├── srq20-form.html (Assessment form)
├── profile.html (User profile)
├── history.html (Assessment history)
├── css/
│   ├── main.css (Global styles)
│   └── components.css (Component styles)
├── js/
│   ├── auth.js (Authentication logic)
│   ├── api.js (API integration)
│   ├── srq20.js (Form logic)
│   └── utils.js (Utility functions)
└── assets/
    └── icons/ (SVG icons)
```

## Technology Stack
- HTML5, CSS3, JavaScript (ES6+)
- Tailwind CSS for styling
- Chart.js for data visualization
- Google OAuth API
- QR Code generation library
- Local Storage for offline data
- Fetch API for backend communication

## API Endpoints (Ready for Backend)
- POST /api/auth/login
- POST /api/auth/register
- POST /api/auth/google
- POST /api/auth/qr-login
- GET /api/user/profile
- POST /api/assessments
- GET /api/assessments/history
- GET /api/analytics/dashboard
