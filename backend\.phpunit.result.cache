{"version": 1, "defects": [], "times": {"Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.007, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.004, "Tests\\Feature\\ApiEndpointsTest::test_home_page_loads_successfully": 0.081, "Tests\\Feature\\ApiEndpointsTest::test_protected_api_routes_require_authentication": 0.022, "Tests\\Feature\\ApiEndpointsTest::test_api_fallback_returns_404": 0.008, "Tests\\Feature\\ApiEndpointsTest::test_qr_login_endpoint_exists": 0.022}}