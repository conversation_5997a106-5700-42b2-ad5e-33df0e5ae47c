{"version": 1, "defects": [], "times": {"Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.012, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.004, "Tests\\Feature\\ApiEndpointsTest::test_home_page_loads_successfully": 0.065, "Tests\\Feature\\ApiEndpointsTest::test_protected_api_routes_require_authentication": 0.022, "Tests\\Feature\\ApiEndpointsTest::test_api_fallback_returns_404": 0.002, "Tests\\Feature\\ApiEndpointsTest::test_qr_login_endpoint_exists": 0.016, "Tests\\Feature\\ApiEndpointsTest::test_api_routes_are_now_public": 0.054, "Tests\\Feature\\ApiEndpointsTest::test_admin_endpoints_are_public": 0.002, "Tests\\Feature\\ApiEndpointsTest::test_dashboard_stats_endpoint": 0.019}}