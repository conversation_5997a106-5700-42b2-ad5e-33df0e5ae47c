@extends('tailadmin-base')

@section('title', 'Hasil Assessment - SantriMental')
@section('dashboard-subtitle', 'Assessment Result')
@section('page-title', 'Hasil Assessment')
@section('page-description', '<PERSON><PERSON>ut adalah hasil assessment kesehatan mental Anda')
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6"><PERSON><PERSON></p>

    <a href="{{ route('dashboard') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Assessment</span>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Hasil</span>
        <div class="ml-auto">
            <span class="tailadmin-badge success text-xs">Baru</span>
        </div>
    </a>
</div>
@endsection

@section('header-actions')
<button class="tailadmin-btn tailadmin-btn-success" onclick="window.location.href='{{ route('assessments') }}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
    </svg>
    Assessment Lagi
</button>

<button class="tailadmin-btn tailadmin-btn-outline" onclick="downloadResult()">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    Download Hasil
</button>
@endsection

@section('breadcrumb')
<a href="{{ route('dashboard') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Dashboard</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<a href="{{ route('assessments') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Assessment</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">Hasil</span>
@endsection

@section('content')
<!-- Result Summary -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="text-center">
        <div class="w-20 h-20 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h2 class="text-3xl font-bold tailadmin-text-primary mb-2">Assessment Selesai!</h2>
        <p class="tailadmin-text-secondary text-lg mb-6">Terima kasih telah menyelesaikan assessment kesehatan mental</p>
        
        <!-- Score Display -->
        <div class="max-w-md mx-auto">
            <div class="bg-success-100 dark:bg-success-900 rounded-2xl p-6 mb-4">
                <div class="text-6xl font-bold text-success-500 mb-2" id="score-display">4</div>
                <div class="text-success-600 dark:text-success-400 font-medium">dari 20 poin</div>
            </div>
            <div class="text-center">
                <span class="tailadmin-badge success text-lg px-4 py-2" id="status-badge">Kondisi Normal</span>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Results -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Interpretation -->
    <div class="tailadmin-card tailadmin-fade-in-up" style="animation-delay: 0.1s;">
        <div class="tailadmin-card-header">
            <h3 class="tailadmin-card-title">Interpretasi Hasil</h3>
        </div>
        <div class="space-y-4">
            <div class="p-4 bg-success-100 dark:bg-success-900 rounded-lg border border-success-200 dark:border-success-800">
                <h4 class="font-semibold text-success-700 dark:text-success-300 mb-2">Status: Normal</h4>
                <p class="text-success-600 dark:text-success-400 text-sm">
                    Hasil assessment menunjukkan bahwa kondisi kesehatan mental Anda dalam batas normal. 
                    Tidak ada indikasi gangguan mental yang signifikan.
                </p>
            </div>
            
            <div class="space-y-3">
                <h4 class="font-semibold tailadmin-text-primary">Penjelasan Skor:</h4>
                <div class="text-sm tailadmin-text-secondary space-y-2">
                    <div class="flex justify-between">
                        <span>Skor 0-7:</span>
                        <span class="text-success-500 font-medium">Normal</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Skor 8-12:</span>
                        <span class="text-warning-500 font-medium">Perlu Perhatian</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Skor 13-20:</span>
                        <span class="text-danger-500 font-medium">Risiko Tinggi</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="tailadmin-card tailadmin-fade-in-up" style="animation-delay: 0.2s;">
        <div class="tailadmin-card-header">
            <h3 class="tailadmin-card-title">Rekomendasi</h3>
        </div>
        <div class="space-y-4">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <svg class="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="font-medium tailadmin-text-primary mb-1">Pertahankan Kondisi</h4>
                    <p class="text-sm tailadmin-text-secondary">Terus jaga pola hidup sehat dan aktivitas positif</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="w-8 h-8 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <svg class="w-4 h-4 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="font-medium tailadmin-text-primary mb-1">Monitoring Berkala</h4>
                    <p class="text-sm tailadmin-text-secondary">Lakukan assessment ulang setiap 3-6 bulan</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <svg class="w-4 h-4 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="font-medium tailadmin-text-primary mb-1">Edukasi Lanjutan</h4>
                    <p class="text-sm tailadmin-text-secondary">Pelajari lebih lanjut tentang kesehatan mental</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.3s;" onclick="window.location.href='{{ route('history') }}'">
        <div class="text-center">
            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="font-semibold tailadmin-text-primary mb-2">Lihat Riwayat</h3>
            <p class="tailadmin-text-secondary text-sm">Pantau perkembangan hasil assessment</p>
        </div>
    </div>

    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.4s;" onclick="window.location.href='{{ route('assessments') }}'">
        <div class="text-center">
            <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
            </div>
            <h3 class="font-semibold tailadmin-text-primary mb-2">Assessment Lagi</h3>
            <p class="tailadmin-text-secondary text-sm">Coba jenis assessment lainnya</p>
        </div>
    </div>

    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.5s;">
        <div class="text-center">
            <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            </div>
            <h3 class="font-semibold tailadmin-text-primary mb-2">Konsultasi</h3>
            <p class="tailadmin-text-secondary text-sm">Diskusi dengan konselor profesional</p>
        </div>
    </div>
</div>

<!-- Assessment Details -->
<div class="tailadmin-card">
    <div class="tailadmin-card-header">
        <h3 class="tailadmin-card-title">Detail Assessment</h3>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Jenis Assessment:</span>
                <span class="tailadmin-text-primary font-medium" id="assessment-type">SRQ-20</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Tanggal:</span>
                <span class="tailadmin-text-primary font-medium" id="assessment-date">-</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Waktu Pengerjaan:</span>
                <span class="tailadmin-text-primary font-medium" id="completion-time">12 menit</span>
            </div>
        </div>
        <div class="space-y-3">
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Total Pertanyaan:</span>
                <span class="tailadmin-text-primary font-medium">20</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Pertanyaan Terjawab:</span>
                <span class="tailadmin-text-primary font-medium">20</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="tailadmin-text-secondary">Tingkat Akurasi:</span>
                <span class="text-success-500 font-medium">100%</span>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const assessmentCode = urlParams.get('code') || 'SRQ20';
    const score = parseInt(urlParams.get('score')) || 4;

    // Update display
    document.getElementById('score-display').textContent = score;
    document.getElementById('assessment-type').textContent = assessmentCode;
    document.getElementById('assessment-date').textContent = new Date().toLocaleDateString('id-ID');

    // Determine status based on score
    let status, statusClass;
    if (score <= 7) {
        status = 'Kondisi Normal';
        statusClass = 'success';
    } else if (score <= 12) {
        status = 'Perlu Perhatian';
        statusClass = 'warning';
    } else {
        status = 'Risiko Tinggi';
        statusClass = 'danger';
    }

    document.getElementById('status-badge').textContent = status;
    document.getElementById('status-badge').className = `tailadmin-badge ${statusClass} text-lg px-4 py-2`;

    // Show completion toast
    setTimeout(() => {
        showToast(`Assessment ${assessmentCode} berhasil diselesaikan dengan skor ${score}/20`, 'success');
    }, 1000);

    // Global functions
    window.downloadResult = () => {
        showToast('Mengunduh hasil assessment...', 'info');
        // Simulate download
        setTimeout(() => {
            showToast('Hasil assessment berhasil diunduh', 'success');
        }, 2000);
    };
});
</script>
@endpush
