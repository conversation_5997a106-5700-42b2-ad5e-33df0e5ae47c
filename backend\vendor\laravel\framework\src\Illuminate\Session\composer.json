{"name": "illuminate/session", "description": "The Illuminate Session package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-ctype": "*", "ext-session": "*", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/filesystem": "^10.0", "illuminate/support": "^10.0", "symfony/finder": "^6.2", "symfony/http-foundation": "^6.4"}, "autoload": {"psr-4": {"Illuminate\\Session\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"illuminate/console": "Required to use the session:table command (^10.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}