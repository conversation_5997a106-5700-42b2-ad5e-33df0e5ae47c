{"name": "illuminate/routing", "description": "The Illuminate Routing package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-filter": "*", "ext-hash": "*", "illuminate/collections": "^10.0", "illuminate/container": "^10.0", "illuminate/contracts": "^10.0", "illuminate/http": "^10.0", "illuminate/macroable": "^10.0", "illuminate/pipeline": "^10.0", "illuminate/session": "^10.0", "illuminate/support": "^10.0", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.2", "symfony/routing": "^6.2"}, "autoload": {"psr-4": {"Illuminate\\Routing\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"illuminate/console": "Required to use the make commands (^10.0).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}