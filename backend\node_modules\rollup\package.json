{"name": "rollup", "version": "3.29.5", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "types": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "scripts": {"build": "rollup --config rollup.config.ts --configPlugin typescript", "dev": "vitepress dev docs", "build:cjs": "rollup --config rollup.config.ts --configPlugin typescript --configTest", "build:bootstrap": "node dist/bin/rollup --config rollup.config.ts --configPlugin typescript", "build:docs": "vitepress build docs", "preview:docs": "vitepress preview docs", "ci:lint": "concurrently 'npm:lint:js:nofix' 'npm:lint:markdown:nofix'", "ci:test": "npm run build:cjs && npm run build:bootstrap && npm run test:all", "ci:test:only": "npm run build:cjs && npm run build:bootstrap && npm run test:only", "ci:coverage": "npm run build:cjs && npm run build:bootstrap && nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "concurrently -c red,green 'npm:lint:js' 'npm:lint:markdown'", "lint:js": "eslint . --fix --cache", "lint:js:nofix": "eslint . --cache", "lint:markdown": "prettier --write \"**/*.md\"", "lint:markdown:nofix": "prettier --check \"**/*.md\"", "perf": "npm run build:cjs && node --expose-gc scripts/perf.js", "perf:init": "node scripts/perf-init.js", "prepare": "husky install && node scripts/check-release.js || npm run build", "prepublishOnly": "node scripts/check-release.js", "release": "node scripts/release.js", "release:docs": "git fetch --update-head-ok origin master:master && git branch --force documentation-published master && git push origin documentation-published", "test": "npm run build && npm run test:all", "test:update-snapshots": "node scripts/update-snapshots.js", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "concurrently --kill-others-on-fail -c green,blue,magenta,cyan,red 'npm:test:only' 'npm:test:browser' 'npm:test:typescript' 'npm:test:leak' 'npm:test:package' 'npm:test:options'", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:options": "node scripts/test-options.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependenciesComments": {"@rollup/plugin-typescript": "It appears that 11.1.3 breaks sourcemaps"}, "devDependencies": {"@codemirror/commands": "^6.2.5", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/language": "^6.9.0", "@codemirror/search": "^6.5.3", "@codemirror/state": "^6.2.1", "@codemirror/view": "^6.19.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@mermaid-js/mermaid-cli": "^10.4.0", "@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-buble": "^1.0.2", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.2.1", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "11.1.2", "@rollup/pluginutils": "^5.0.4", "@types/estree": "1.0.1", "@types/mocha": "^10.0.1", "@types/node": "~14.18.61", "@types/yargs-parser": "^21.0.0", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "acorn": "^8.10.0", "acorn-import-assertions": "^1.9.0", "acorn-jsx": "^5.3.2", "acorn-walk": "^8.2.0", "buble": "^0.20.0", "builtin-modules": "^3.3.0", "chokidar": "^3.5.3", "colorette": "^2.0.20", "concurrently": "^8.2.1", "core-js": "^3.32.2", "date-time": "^4.0.0", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-plugin-vue": "^9.17.0", "fixturify": "^3.0.0", "flru": "^1.0.2", "fs-extra": "^11.1.1", "github-api": "^3.4.0", "hash.js": "^1.1.7", "husky": "^8.0.3", "inquirer": "^9.2.11", "is-reference": "^3.0.2", "lint-staged": "^14.0.1", "locate-character": "^3.0.0", "magic-string": "^0.30.3", "mocha": "^10.2.0", "nyc": "^15.1.0", "pinia": "^2.1.6", "prettier": "^3.0.3", "pretty-bytes": "^6.1.1", "pretty-ms": "^8.0.0", "requirejs": "^2.3.6", "rollup": "^3.29.2", "rollup-plugin-license": "^3.1.0", "rollup-plugin-string": "^3.0.0", "rollup-plugin-thatworks": "^1.0.4", "semver": "^7.5.4", "shx": "^0.3.4", "signal-exit": "^4.1.0", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "systemjs": "^6.14.2", "terser": "^5.19.4", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^4.4.9", "vitepress": "^1.0.0-rc.14", "vue": "^3.3.4", "weak-napi": "^2.0.2", "yargs-parser": "^21.1.1"}, "overrides": {"semver": "^7.5.4"}, "files": ["dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/es/package.json"], "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "exports": {".": {"types": "./dist/rollup.d.ts", "require": "./dist/rollup.js", "import": "./dist/es/rollup.js"}, "./loadConfigFile": {"types": "./dist/loadConfigFile.d.ts", "require": "./dist/loadConfigFile.js", "default": "./dist/loadConfigFile.js"}, "./getLogFilter": {"types": "./dist/getLogFilter.d.ts", "require": "./dist/getLogFilter.js", "import": "./dist/es/getLogFilter.js"}, "./dist/*": "./dist/*"}}