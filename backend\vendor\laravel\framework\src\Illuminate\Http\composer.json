{"name": "illuminate/http", "description": "The Illuminate Http package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-filter": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "illuminate/collections": "^10.0", "illuminate/macroable": "^10.0", "illuminate/session": "^10.0", "illuminate/support": "^10.0", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.2", "symfony/mime": "^6.2"}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "suggest": {"ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "guzzlehttp/guzzle": "Required to use the HTTP Client (^7.5)."}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}