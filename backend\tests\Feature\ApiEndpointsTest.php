<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiEndpointsTest extends TestCase
{
    /**
     * Test that the home page loads successfully.
     */
    public function test_home_page_loads_successfully(): void
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertSee('SantriMental');
    }

    /**
     * Test that protected API routes require authentication.
     */
    public function test_protected_api_routes_require_authentication(): void
    {
        $response = $this->getJson('/api/forms');
        $response->assertStatus(401);
    }

    /**
     * Test that API fallback route returns 404.
     */
    public function test_api_fallback_returns_404(): void
    {
        $response = $this->getJson('/api/nonexistent-endpoint');
        $response->assertStatus(404);
        $response->assertJson(['message' => 'Route not found.']);
    }

    /**
     * Test that QR login endpoint exists (public endpoint).
     */
    public function test_qr_login_endpoint_exists(): void
    {
        $response = $this->postJson('/api/auth/qr', []);
        // Should return validation error or method not allowed, not 404
        $this->assertNotEquals(404, $response->getStatusCode());
    }
}
