# SantriMental - Laragon Setup Guide

## Opsi 1: Virtual Host Manual (Recommended)

### Langkah-langkah:

1. **Copy Virtual Host Config**:
   - Copy file `santrimental.conf` ke `C:\laragon\etc\apache2\sites-enabled\`
   - Atau buat file baru di folder tersebut dengan nama `santrimental.conf`

2. **Update Hosts File**:
   - Run `update-hosts.bat` as Administrator
   - Atau manual edit `C:\Windows\System32\drivers\etc\hosts`
   - Tambahkan: `127.0.0.1    santrimental.me`

3. **Restart Laragon**:
   - Stop Laragon
   - Start Laragon
   - Akses: `http://santrimental.me`

## Opsi 2: Symlink (Mudah)

### Langkah-langkah:

1. **Run Symlink Script**:
   - Run `create-symlink.bat` as Administrator
   - Akses: `http://localhost/santrimental-public`

## Opsi 3: Auto Virtual Host Laragon

### Langkah-langkah:

1. **Rename Folder**:
   - Rename folder `santrimental` menjadi `santrimental.me`
   - Pindahkan isi `backend/public` ke root `santrimental.me`

2. **Enable Auto Virtual Host**:
   - <PERSON>uka Laragon
   - Klik kanan → Preferences → General
   - Centang "Auto Virtual Hosts"
   - Restart Laragon

3. **Akses**:
   - `http://santrimental.me`

## Opsi 4: .htaccess Redirect (Sudah Aktif)

### Sudah dikonfigurasi:
- File `.htaccess` di root sudah diupdate
- Akses `http://localhost/santrimental/` akan redirect ke `backend/public`

## Testing URLs

Setelah konfigurasi, test URL berikut:

- ✅ `http://santrimental.me` (Opsi 1)
- ✅ `http://localhost/santrimental-public` (Opsi 2)  
- ✅ `http://localhost/santrimental/` (Opsi 4)

## Troubleshooting

### Jika santrimental.me tidak bisa diakses:
1. Cek hosts file: `C:\Windows\System32\drivers\etc\hosts`
2. Restart browser
3. Clear DNS cache: `ipconfig /flushdns`
4. Restart Laragon

### Jika masih error:
1. Cek Apache error log: `C:\laragon\logs\`
2. Pastikan folder permissions benar
3. Cek .htaccess syntax

## Recommended Setup

**Untuk Development**: Gunakan Opsi 4 (.htaccess redirect)
**Untuk Production-like**: Gunakan Opsi 1 (Virtual Host)
**Untuk Quick Test**: Gunakan Opsi 2 (Symlink)
