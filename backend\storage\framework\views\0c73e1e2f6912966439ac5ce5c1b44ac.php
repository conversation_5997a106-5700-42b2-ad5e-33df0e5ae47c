<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Admin Dashboard - SantriMental</title>

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="<?php echo e(asset('css/modern-dashboard.css')); ?>">

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
</head>
<body class="gradient-mesh min-h-screen transition-all duration-300">

    <!-- Modern Admin Header -->
    <nav class="header-modern sticky top-0 z-50">
        <div class="flex items-center justify-between p-6 max-w-7xl mx-auto">
            <div class="flex items-center">
                <div class="w-12 h-12 gradient-secondary rounded-xl flex items-center justify-center mr-4 shadow-lg">
                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-white">SantriMental Admin</h1>
                    <p class="text-white/60 text-sm">Control Panel Administrator</p>
                </div>
            </div>

            <!-- Admin Navigation -->
            <div class="hidden md:flex items-center space-x-6">
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Dashboard</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Users</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Reports</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Settings</a>
            </div>

            <div class="flex items-center space-x-4">
                <!-- System Status -->
                <div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-green-500/20 rounded-lg border border-green-500/30">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-green-400 text-sm font-medium">System Online</span>
                </div>

                <!-- Admin Quick Actions -->
                <button class="btn-ghost p-3 rounded-xl relative" data-tooltip="System Alerts">
                    <i data-lucide="alert-triangle" class="w-5 h-5"></i>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full"></div>
                </button>

                <button class="btn-ghost p-3 rounded-xl" data-tooltip="System Monitor">
                    <i data-lucide="activity" class="w-5 h-5"></i>
                </button>

                <button id="theme-toggle" class="btn-ghost p-3 rounded-xl" data-tooltip="Toggle Theme">
                    <i data-lucide="moon" class="w-5 h-5"></i>
                </button>

                <!-- Admin Profile -->
                <div class="flex items-center space-x-3 ml-4 pl-4 border-l border-white/20">
                    <div class="text-right">
                        <p class="text-white font-medium text-sm">Super Admin</p>
                        <p class="text-white/60 text-xs"><EMAIL></p>
                    </div>
                    <div class="w-10 h-10 gradient-danger rounded-xl flex items-center justify-center">
                        <i data-lucide="crown" class="w-5 h-5 text-white"></i>
                    </div>
                    <button onclick="logout()" class="btn-ghost p-2 rounded-lg text-red-400 hover:bg-red-500/10" data-tooltip="Logout">
                        <i data-lucide="log-out" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-6 py-8 space-y-8">

        <!-- Welcome Banner -->
        <div class="modern-card p-8 animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-16 h-16 gradient-secondary rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                        <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-white mb-2">Admin Control Panel</h2>
                        <p class="text-white/70 text-lg">Kelola sistem SantriMental dengan mudah dan efisien</p>
                        <div class="flex items-center mt-3 space-x-4">
                            <div class="flex items-center text-sm text-green-400">
                                <i data-lucide="server" class="w-4 h-4 mr-1"></i>
                                <span>Server Online</span>
                            </div>
                            <div class="flex items-center text-sm text-blue-400">
                                <i data-lucide="database" class="w-4 h-4 mr-1"></i>
                                <span>Database Connected</span>
                            </div>
                            <div class="flex items-center text-sm text-purple-400">
                                <i data-lucide="shield" class="w-4 h-4 mr-1"></i>
                                <span>Security Active</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="text-right">
                        <p class="text-white/60 text-sm">Last Login</p>
                        <p class="text-white font-semibold">Today, 09:30 AM</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Users Card -->
            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-primary rounded-xl shadow-lg">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                        +8%
                    </div>
                </div>
                <div>
                    <p class="stats-label">Total Pengguna</p>
                    <p id="total-users" class="stats-value">1,247</p>
                    <p class="text-xs text-white/50 mt-1">Pengguna terdaftar</p>
                </div>
            </div>

            <!-- Total Students Card -->
            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-success rounded-xl shadow-lg">
                        <i data-lucide="graduation-cap" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="plus" class="w-3 h-3 mr-1"></i>
                        +12
                    </div>
                </div>
                <div>
                    <p class="stats-label">Total Siswa</p>
                    <p id="total-siswa" class="stats-value">892</p>
                    <p class="text-xs text-white/50 mt-1">Siswa aktif</p>
                </div>
            </div>

            <!-- Total Teachers Card -->
            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-secondary rounded-xl shadow-lg">
                        <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="plus" class="w-3 h-3 mr-1"></i>
                        +3
                    </div>
                </div>
                <div>
                    <p class="stats-label">Total Guru</p>
                    <p id="total-guru" class="stats-value">67</p>
                    <p class="text-xs text-white/50 mt-1">Tenaga pengajar</p>
                </div>
            </div>

            <!-- Total Assessments Card -->
            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.5s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-warning rounded-xl shadow-lg">
                        <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                        +24%
                    </div>
                </div>
                <div>
                    <p class="stats-label">Total Skrining</p>
                    <p id="total-assessments" class="stats-value">3,456</p>
                    <p class="text-xs text-white/50 mt-1">Tes selesai</p>
                </div>
            </div>
        </div>

        <!-- Admin Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="modern-card p-6 animate-fade-in-up group hover:scale-105" style="animation-delay: 0.6s;">
                <div class="flex items-center mb-6">
                    <div class="p-4 bg-blue-500/20 rounded-2xl mr-4 group-hover:scale-110 transition-transform">
                        <i data-lucide="users" class="w-8 h-8 text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white">Kelola Pengguna</h3>
                        <p class="text-sm text-white/60">Manajemen user sistem</p>
                    </div>
                </div>
                <p class="text-white/70 text-sm mb-4">Tambah, edit, atau hapus pengguna. Kelola peran dan izin akses sistem.</p>
                <button class="btn-primary w-full">
                    <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                    Kelola Users
                </button>
            </div>

            <div class="modern-card p-6 animate-fade-in-up group hover:scale-105" style="animation-delay: 0.7s;">
                <div class="flex items-center mb-6">
                    <div class="p-4 bg-purple-500/20 rounded-2xl mr-4 group-hover:scale-110 transition-transform">
                        <i data-lucide="bar-chart-3" class="w-8 h-8 text-purple-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white">Laporan Sistem</h3>
                        <p class="text-sm text-white/60">Analytics & reports</p>
                    </div>
                </div>
                <p class="text-white/70 text-sm mb-4">Lihat statistik penggunaan, laporan kesehatan mental, dan analisis data.</p>
                <button class="btn-secondary w-full">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Lihat Laporan
                </button>
            </div>

            <div class="modern-card p-6 animate-fade-in-up group hover:scale-105" style="animation-delay: 0.8s;">
                <div class="flex items-center mb-6">
                    <div class="p-4 bg-green-500/20 rounded-2xl mr-4 group-hover:scale-110 transition-transform">
                        <i data-lucide="settings" class="w-8 h-8 text-green-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white">Pengaturan Sistem</h3>
                        <p class="text-sm text-white/60">System configuration</p>
                    </div>
                </div>
                <p class="text-white/70 text-sm mb-4">Konfigurasi sistem, backup data, dan pengaturan keamanan.</p>
                <button class="btn-ghost w-full">
                    <i data-lucide="cog" class="w-4 h-4 mr-2"></i>
                    Pengaturan
                </button>
            </div>
        </div>
                <p class="text-purple-200 text-sm mb-4">Tambah, edit, atau hapus pengguna sistem</p>
                <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Kelola Pengguna
                </button>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Kelola Assessment</h3>
                <p class="text-purple-200 text-sm mb-4">Konfigurasi form dan aturan assessment</p>
                <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Kelola Assessment
                </button>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Laporan</h3>
                <p class="text-purple-200 text-sm mb-4">Lihat laporan dan statistik sistem</p>
                <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Lihat Laporan
                </button>
            </div>
        </div>

        <!-- Recent Assessments -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Assessment Terbaru</h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-white/20">
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Siswa</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Assessment</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Skor</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Status</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Tanggal</th>
                        </tr>
                    </thead>
                    <tbody id="recent-assessments">
                        <tr>
                            <td colspan="5" class="text-center text-purple-200 py-8">Memuat data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <script src="<?php echo e(asset('js/auth.js')); ?>"></script>
    <script src="<?php echo e(asset('js/utils.js')); ?>"></script>
    <script src="<?php echo e(asset('js/modern-components.js')); ?>"></script>
    <script src="<?php echo e(asset('js/performance-optimizer.js')); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const auth = window.auth;
            const Utils = window.Utils;

            // Check authentication and role (temporarily disabled)
            // if (!auth.isAuthenticated()) {
            //     window.location.href = '/';
            //     return;
            // }

            // Load admin dashboard data (with mock data for testing)
            try {
                // Use mock data for testing
                const mockAdminData = {
                    success: true,
                    data: {
                        stats: {
                            total_users: 156,
                            total_siswa: 120,
                            total_guru: 25,
                            total_assessments: 450
                        },
                        recent_assessments: [
                            { date: '2024-01-15', user: 'Ahmad Rizki', action: 'Completed SRQ-20 Assessment' },
                            { date: '2024-01-15', user: 'Siti Nurhaliza', action: 'Registered new account' },
                            { date: '2024-01-14', user: 'Budi Santoso', action: 'Updated profile information' }
                        ]
                    }
                };

                displayAdminData(mockAdminData.data);

                // Show success notification
                if (window.modernComponents) {
                    setTimeout(() => {
                        window.modernComponents.showNotification(
                            'Admin dashboard loaded!',
                            'success',
                            3000,
                            {
                                description: 'Mock data loaded for testing purposes'
                            }
                        );
                    }, 1500);
                }

            } catch (error) {
                console.error('Failed to load admin data:', error);
                if (window.modernComponents) {
                    window.modernComponents.showNotification('Failed to load admin data', 'error');
                }
            }

            function displayAdminData(data) {
                const { stats, recent_assessments } = data;

                // Update stats
                document.getElementById('total-users').textContent = stats.total_users || 0;
                document.getElementById('total-siswa').textContent = stats.total_siswa || 0;
                document.getElementById('total-guru').textContent = stats.total_guru || 0;
                document.getElementById('total-assessments').textContent = stats.total_assessments || 0;

                // Update recent assessments table
                const tbody = document.getElementById('recent-assessments');
                if (recent_assessments && recent_assessments.length > 0) {
                    tbody.innerHTML = recent_assessments.map(assessment => {
                        const statusColors = {
                            'normal': 'bg-green-500/20 text-green-300',
                            'concern': 'bg-yellow-500/20 text-yellow-300',
                            'high_risk': 'bg-red-500/20 text-red-300',
                            'severe': 'bg-red-600/20 text-red-200'
                        };

                        return `
                            <tr class="border-b border-white/10">
                                <td class="py-3 text-white">${assessment.user.first_name} ${assessment.user.last_name}</td>
                                <td class="py-3 text-purple-200">${assessment.form_template.name}</td>
                                <td class="py-3 text-white">${assessment.total_score}</td>
                                <td class="py-3">
                                    <span class="px-2 py-1 rounded-full text-xs ${statusColors[assessment.status] || 'bg-gray-500/20 text-gray-300'}">
                                        ${assessment.status}
                                    </span>
                                </td>
                                <td class="py-3 text-purple-200">${new Date(assessment.created_at).toLocaleDateString('id-ID')}</td>
                            </tr>
                        `;
                    }).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center text-purple-200 py-8">Belum ada assessment</td></tr>';
                }
            }

            // Logout function
            window.logout = async () => {
                try {
                    await auth.logout();
                    window.location.href = '/';
                } catch (error) {
                    console.error('Logout failed:', error);
                }
            };
        });
    </script>

    <!-- Modern Dashboard JavaScript -->
    <script src="<?php echo e(asset('js/modern-dashboard.js')); ?>"></script>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize admin dashboard features
        document.addEventListener('DOMContentLoaded', () => {
            // Animate stats values
            const statsValues = document.querySelectorAll('.stats-value');
            statsValues.forEach(el => {
                const finalValue = parseInt(el.textContent);
                if (!isNaN(finalValue) && finalValue > 0) {
                    window.modernDashboard.animateValue(el, 0, finalValue, 1500);
                }
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.stats-card, .modern-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\santrimental1\backend\resources\views/admin-dashboard.blade.php ENDPATH**/ ?>