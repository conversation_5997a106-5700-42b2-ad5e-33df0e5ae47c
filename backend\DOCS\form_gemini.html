<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> SRQ-29</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Libraries for PDF Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .question-group-title {
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 600;
        }
        .result-card {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .result-card.show {
            display: block;
            opacity: 1;
        }
        .export-buttons {
            display: none;
        }
        /* Print-specific styles */
        @media print {
            body * {
                visibility: hidden;
            }
            #printableArea, #printableArea * {
                visibility: visible;
            }
            #printableArea {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div class="container mx-auto max-w-4xl p-4 sm:p-6 md:p-8">
        <div id="main-content" class="bg-white p-6 sm:p-8 rounded-2xl shadow-lg">
            <div class="text-center mb-6 no-print">
                <h1 class="text-3xl font-bold text-gray-900">Penilaian Mandiri Kesehatan Jiwa SRQ-29</h1>
                <p class="mt-2 text-gray-600">Dikembangkan oleh Organisasi Kesehatan Dunia (WHO)</p>
            </div>

            <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-4 rounded-md my-6 no-print" role="alert">
                <p class="font-bold">Petunjuk & Sangkalan</p>
                <p>Mohon jawab pertanyaan berikut berdasarkan apa yang Anda rasakan selama **30 hari terakhir**. Ini adalah alat skrining, bukan instrumen diagnostik. Hasilnya bersifat indikatif dan tidak boleh menggantikan konsultasi dengan profesional kesehatan jiwa yang berkualifikasi.</p>
            </div>

            <form id="srqForm" class="no-print">
                <!-- Question Groups -->
                <h2 class="question-group-title text-gray-700">Kesejahteraan Umum & Kesehatan Emosional</h2>
                <div id="questions-1-20" class="space-y-4"></div>
                <h2 class="question-group-title text-gray-700">Penggunaan Zat</h2>
                <div id="questions-21" class="space-y-4"></div>
                <h2 class="question-group-title text-gray-700">Pengalaman Persepsi & Pikiran</h2>
                <div id="questions-22-24" class="space-y-4"></div>
                <h2 class="question-group-title text-gray-700">Gejala Stres Pasca-Trauma</h2>
                <p class="text-sm text-gray-500 mb-4">Pertanyaan-pertanyaan ini berkaitan dengan peristiwa menyedihkan atau bencana yang mungkin pernah Anda alami.</p>
                <div id="questions-25-29" class="space-y-4"></div>

                <div class="mt-8 text-center">
                    <button type="button" id="calculateBtn" class="bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-4 focus:ring-indigo-300 transition-all duration-300 transform hover:scale-105">
                        Tampilkan Hasil Saya
                    </button>
                </div>
            </form>

            <!-- Results Section -->
            <div id="results" class="mt-10 result-card">
                <div id="printableArea">
                    <h2 class="text-2xl font-bold text-center mb-6 text-gray-900">Hasil Penilaian Anda</h2>
                    <div class="space-y-4">
                        <div id="neuroticResult" class="p-4 rounded-lg"></div>
                        <div id="substanceResult" class="p-4 rounded-lg"></div>
                        <div id="psychoticResult" class="p-4 rounded-lg"></div>
                        <div id="ptsdResult" class="p-4 rounded-lg"></div>
                    </div>
                    <div id="summary-for-export" class="hidden"></div>
                     <div class="bg-yellow-50 border-l-4 border-yellow-500 text-yellow-800 p-4 rounded-md mt-8" role="alert">
                        <p class="font-bold">Catatan Penting:</p>
                        <p>Jika hasil Anda menunjukkan potensi masalah di area mana pun, sangat disarankan untuk berbicara dengan dokter, konselor, atau profesional kesehatan jiwa. Mereka dapat memberikan evaluasi yang komprehensif dan mendiskusikan pilihan dukungan dan perawatan yang tepat.</p>
                    </div>
                </div>

                <!-- Export Buttons -->
                <div id="export-buttons" class="mt-8 pt-6 border-t border-gray-200 text-center space-y-2 sm:space-y-0 sm:space-x-2 no-print">
                    <button id="printBtn" class="bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg hover:bg-gray-800 transition-colors">Cetak Hasil</button>
                    <button id="pdfBtn" class="bg-red-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">Ekspor ke PDF</button>
                    <button id="excelBtn" class="bg-green-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">Ekspor ke Excel</button>
                    <button id="csvBtn" class="bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">Ekspor ke CSV</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { jsPDF } = window.jspdf;
        
        const questions = [
            // Neurotic symptoms (1-20)
            { "id": 1, "text": "Apakah Anda sering menderita sakit kepala?" },
            { "id": 2, "text": "Apakah Anda kehilangan nafsu makan?" },
            { "id": 3, "text": "Apakah tidur Anda tidak lelap?" },
            { "id": 4, "text": "Apakah Anda mudah menjadi takut?" },
            { "id": 5, "text": "Apakah tangan Anda gemetar?" },
            { "id": 6, "text": "Apakah Anda merasa cemas, tegang dan khawatir?" },
            { "id": 7, "text": "Apakah Anda mengalami gangguan pencernaan?" },
            { "id": 8, "text": "Apakah Anda merasa sulit berpikir jernih?" },
            { "id": 9, "text": "Apakah Anda merasa tidak bahagia?" },
            { "id": 10, "text": "Apakah Anda lebih sering menangis?" },
            { "id": 11, "text": "Apakah Anda merasa sulit untuk menikmati aktivitas sehari-hari?" },
            { "id": 12, "text": "Apakah Anda mengalami kesulitan untuk mengambil keputusan?" },
            { "id": 13, "text": "Apakah aktivitas/tugas sehari-hari Anda terbengkalai?" },
            { "id": 14, "text": "Apakah Anda merasa tidak mampu berperan dalam kehidupan ini?" },
            { "id": 15, "text": "Apakah Anda kehilangan minat terhadap banyak hal?" },
            { "id": 16, "text": "Apakah Anda merasa tidak berharga?" },
            { "id": 17, "text": "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?" },
            { "id": 18, "text": "Apakah Anda merasa lelah sepanjang waktu?" },
            { "id": 19, "text": "Apakah Anda merasa tidak enak di perut?" },
            { "id": 20, "text": "Apakah Anda mudah lelah?" },
            // Substance use (21)
            { "id": 21, "text": "Apakah Anda minum alkohol lebih banyak dari biasanya atau apakah Anda menggunakan narkoba?" },
            // Psychotic symptoms (22-24)
            { "id": 22, "text": "Apakah Anda yakin bahwa seseorang mencoba mencelakai Anda dengan cara tertentu?" },
            { "id": 23, "text": "Apakah ada yang mengganggu atau hal yang tidak biasa dalam pikiran Anda?" },
            { "id": 24, "text": "Apakah Anda pernah mendengar suara tanpa tahu sumbernya atau yang orang lain tidak dapat mendengar?" },
            // PTSD (25-29)
            { "id": 25, "text": "Apakah Anda mengalami mimpi yang mengganggu tentang suatu bencana/musibah atau adakah saat-saat Anda seolah mengalami kembali kejadian bencana itu?" },
            { "id": 26, "text": "Apakah Anda menghindari kegiatan, tempat, orang atau pikiran yang mengingatkan Anda akan bencana tersebut?" },
            { "id": 27, "text": "Apakah minat Anda terhadap teman dan kegiatan Anda yang biasa Anda lakukan berkurang?" },
            { "id": 28, "text": "Apakah Anda merasa sangat terganggu jika berada dalam situasi yang mengingatkan Anda akan bencana atau jika Anda berpikir tentang bencana itu?" },
            { "id": 29, "text": "Apakah Anda kesulitan memahami atau mengekspresikan perasaan Anda?" },
        ];

        function createQuestionElement(q) {
            return `
                <div class="bg-gray-100 p-4 rounded-lg flex items-center justify-between transition-all duration-200">
                    <label for="q${q.id}" class="flex-1 text-gray-700">${q.id}. ${q.text}</label>
                    <div class="flex space-x-2">
                        <input type="radio" id="q${q.id}_yes" name="q${q.id}" value="1" class="hidden peer/yes">
                        <label for="q${q.id}_yes" class="cursor-pointer px-4 py-2 rounded-md text-sm font-medium border border-gray-300 peer-checked/yes:bg-red-500 peer-checked/yes:text-white peer-checked/yes:border-red-500">Ya</label>
                        <input type="radio" id="q${q.id}_no" name="q${q.id}" value="0" class="hidden peer/no" checked>
                        <label for="q${q.id}_no" class="cursor-pointer px-4 py-2 rounded-md text-sm font-medium border border-gray-300 peer-checked/no:bg-green-500 peer-checked/no:text-white peer-checked/no:border-green-500">Tidak</label>
                    </div>
                </div>`;
        }
        
        const questions1to20Container = document.getElementById('questions-1-20');
        const questions21Container = document.getElementById('questions-21');
        const questions22to24Container = document.getElementById('questions-22-24');
        const questions25to29Container = document.getElementById('questions-25-29');

        questions.forEach(q => {
            const questionHTML = createQuestionElement(q);
            if (q.id <= 20) questions1to20Container.innerHTML += questionHTML;
            else if (q.id === 21) questions21Container.innerHTML += questionHTML;
            else if (q.id >= 22 && q.id <= 24) questions22to24Container.innerHTML += questionHTML;
            else questions25to29Container.innerHTML += questionHTML;
        });

        document.getElementById('calculateBtn').addEventListener('click', () => {
            const form = document.getElementById('srqForm');
            const formData = new FormData(form);
            let scores = { neurotic: 0, substance: 0, psychotic: 0, ptsd: 0 };
            for (let i = 1; i <= 29; i++) {
                const answer = parseInt(formData.get(`q${i}`), 10) || 0;
                if (i <= 20) scores.neurotic += answer;
                else if (i === 21) scores.substance += answer;
                else if (i >= 22 && i <= 24) scores.psychotic += answer;
                else scores.ptsd += answer;
            }
            displayResults(scores);
        });
        
        function displayResults(scores) {
            const resultsContainer = document.getElementById('results');
            const neuroticResult = document.getElementById('neuroticResult');
            const substanceResult = document.getElementById('substanceResult');
            const psychoticResult = document.getElementById('psychoticResult');
            const ptsdResult = document.getElementById('ptsdResult');
            const summaryContainer = document.getElementById('summary-for-export');
            
            let summaryText = '';

            // Anxiety/Depression Score
            if (scores.neurotic >= 5) {
                neuroticResult.className = 'p-5 rounded-lg bg-red-100 border border-red-200';
                neuroticResult.innerHTML = `<h3 class="font-bold text-red-800">Gejala Emosional & Kecemasan</h3><p class="text-red-700 mt-1">Skor Anda ${scores.neurotic} (dari 20) menunjukkan Anda mungkin mengalami gejala yang berhubungan dengan kecemasan dan/atau depresi. Dianjurkan untuk membicarakan perasaan ini dengan seorang profesional kesehatan.</p>`;
                summaryText += `Gejala Emosional & Kecemasan: Skor ${scores.neurotic}/20. Menunjukkan kemungkinan gejala kecemasan/depresi.\n`;
            } else {
                neuroticResult.className = 'p-5 rounded-lg bg-green-100 border border-green-200';
                neuroticResult.innerHTML = `<h3 class="font-bold text-green-800">Gejala Emosional & Kecemasan</h3><p class="text-green-700 mt-1">Skor Anda ${scores.neurotic} (dari 20) berada dalam rentang tipikal, menunjukkan kemungkinan yang lebih rendah dari gangguan kecemasan atau depresi klinis saat ini.</p>`;
                summaryText += `Gejala Emosional & Kecemasan: Skor ${scores.neurotic}/20. Dalam rentang tipikal.\n`;
            }

            // Substance Use
            if (scores.substance >= 1) {
                substanceResult.className = 'p-5 rounded-lg bg-red-100 border border-red-200';
                substanceResult.innerHTML = `<h3 class="font-bold text-red-800">Penggunaan Zat</h3><p class="text-red-700 mt-1">Anda mengindikasikan peningkatan penggunaan alkohol atau obat-obatan. Hal ini dapat berdampak signifikan pada kesehatan mental dan mungkin memerlukan diskusi dengan seorang profesional.</p>`;
                summaryText += `Penggunaan Zat: Terindikasi peningkatan penggunaan.\n`;
            } else {
                 substanceResult.className = 'p-5 rounded-lg bg-green-100 border border-green-200';
                 substanceResult.innerHTML = `<h3 class="font-bold text-green-800">Penggunaan Zat</h3><p class="text-green-700 mt-1">Anda tidak mengindikasikan adanya peningkatan penggunaan alkohol atau obat-obatan.</p>`;
                 summaryText += `Penggunaan Zat: Tidak ada indikasi peningkatan.\n`;
            }
            
            // Psychotic Symptoms
            if (scores.psychotic >= 1) {
                psychoticResult.className = 'p-5 rounded-lg bg-red-100 border border-red-200';
                psychoticResult.innerHTML = `<h3 class="font-bold text-red-800">Pengalaman Persepsi & Pikiran</h3><p class="text-red-700 mt-1">Anda menjawab 'Ya' pada satu atau lebih pertanyaan yang berkaitan dengan pikiran atau pengalaman yang tidak biasa. Sangat penting untuk mendiskusikan pengalaman ini dengan dokter atau profesional kesehatan mental untuk evaluasi lebih lanjut.</p>`;
                summaryText += `Pengalaman Persepsi & Pikiran: Terindikasi adanya gejala. Sangat disarankan konsultasi.\n`;
            } else {
                psychoticResult.className = 'p-5 rounded-lg bg-green-100 border border-green-200';
                psychoticResult.innerHTML = `<h3 class="font-bold text-green-800">Pengalaman Persepsi & Pikiran</h3><p class="text-green-700 mt-1">Anda tidak melaporkan gejala yang berkaitan dengan pikiran atau pengalaman yang tidak biasa.</p>`;
                summaryText += `Pengalaman Persepsi & Pikiran: Tidak ada indikasi gejala.\n`;
            }

            // PTSD Symptoms
            if (scores.ptsd >= 1) {
                ptsdResult.className = 'p-5 rounded-lg bg-red-100 border border-red-200';
                ptsdResult.innerHTML = `<h3 class="font-bold text-red-800">Gejala Stres Pasca-Trauma</h3><p class="text-red-700 mt-1">Anda menjawab 'Ya' pada satu atau lebih pertanyaan yang berkaitan dengan stres pasca-trauma. Jika Anda pernah mengalami peristiwa traumatis, perasaan ini biasa terjadi, tetapi berbicara dengan spesialis bisa sangat membantu untuk pemulihan.</p>`;
                 summaryText += `Gejala Stres Pasca-Trauma: Terindikasi adanya gejala.\n`;
            } else {
                ptsdResult.className = 'p-5 rounded-lg bg-green-100 border border-green-200';
                ptsdResult.innerHTML = `<h3 class="font-bold text-green-800">Gejala Stres Pasca-Trauma</h3><p class="text-green-700 mt-1">Anda tidak melaporkan gejala yang biasa dikaitkan dengan PTSD.</p>`;
                summaryText += `Gejala Stres Pasca-Trauma: Tidak ada indikasi gejala.\n`;
            }
            
            summaryContainer.innerText = summaryText;
            resultsContainer.classList.add('show');
            document.getElementById('export-buttons').style.display = 'block';
            resultsContainer.scrollIntoView({ behavior: 'smooth' });
        }

        // --- EXPORT AND PRINT FUNCTIONS ---

        // 1. Print Function
        document.getElementById('printBtn').addEventListener('click', () => {
            window.print();
        });

        // 2. PDF Export Function
        document.getElementById('pdfBtn').addEventListener('click', () => {
            const content = document.getElementById('printableArea');
            const btn = document.getElementById('pdfBtn');
            btn.innerText = "Memproses...";
            btn.disabled = true;

            html2canvas(content, { scale: 2 }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
                pdf.save('Hasil_Penilaian_SRQ29.pdf');
                btn.innerText = "Ekspor ke PDF";
                btn.disabled = false;
            });
        });

        // Function to get data for CSV/Excel
        function getExportData() {
            const formData = new FormData(document.getElementById('srqForm'));
            let data = "No,Pertanyaan,Jawaban\n";
            questions.forEach(q => {
                const answerValue = formData.get(`q${q.id}`);
                const answerText = answerValue === '1' ? 'Ya' : 'Tidak';
                // Sanitize question text for CSV (remove commas)
                const sanitizedQuestion = `"${q.text.replace(/"/g, '""')}"`;
                data += `${q.id},${sanitizedQuestion},${answerText}\n`;
            });
            
            data += "\nRingkasan Hasil\n";
            const summaryText = document.getElementById('summary-for-export').innerText;
            data += `"${summaryText.replace(/"/g, '""')}"`;

            return data;
        }

        function downloadFile(data, filename, type) {
             const blob = new Blob([data], { type: `${type};charset=utf-8;` });
             const link = document.createElement('a');
             if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
             }
        }

        // 3. Excel Export Function
        document.getElementById('excelBtn').addEventListener('click', () => {
            const data = getExportData();
            downloadFile(data, 'Hasil_Penilaian_SRQ29.xls', 'application/vnd.ms-excel');
        });

        // 4. CSV Export Function
        document.getElementById('csvBtn').addEventListener('click', () => {
             const data = getExportData();
             downloadFile(data, 'Hasil_Penilaian_SRQ29.csv', 'text/csv');
        });

    </script>
</body>
</html>
