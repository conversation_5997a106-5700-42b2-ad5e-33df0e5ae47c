<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Dashboard Guru - SantriMental</title>

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="<?php echo e(asset('css/modern-dashboard.css')); ?>">

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body class="gradient-mesh min-h-screen">
    
    <!-- Navigation Header -->
    <nav class="glass-card p-4 mb-6">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">G</span>
                </div>
                <h1 class="text-xl font-bold text-white">Dashboard Guru</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-purple-200 text-sm" id="teacher-name">Guru</span>
                <button onclick="logout()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Logout
                </button>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4">
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Total Siswa</p>
                        <p id="total-students" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">👨‍🎓</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Assessment Bulan Ini</p>
                        <p id="monthly-assessments" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📋</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Perlu Perhatian</p>
                        <p id="students-attention" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">⚠️</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Daftar Siswa</h3>
                <p class="text-purple-200 text-sm mb-4">Lihat dan kelola data siswa di kelas Anda</p>
                <button onclick="showStudentsList()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Lihat Daftar Siswa
                </button>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Laporan Kelas</h3>
                <p class="text-purple-200 text-sm mb-4">Analisis hasil assessment seluruh kelas</p>
                <button onclick="showClassReport()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Lihat Laporan
                </button>
            </div>
        </div>

        <!-- Students List -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <h3 class="text-lg font-semibold text-white mb-4">Daftar Siswa</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="students-grid">
                <div class="text-center text-purple-200 py-8">Memuat data siswa...</div>
            </div>
        </div>

        <!-- Recent Assessments -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Assessment Terbaru</h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-white/20">
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Siswa</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Kelas</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Assessment</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Skor</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Status</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Tanggal</th>
                        </tr>
                    </thead>
                    <tbody id="recent-assessments">
                        <tr>
                            <td colspan="6" class="text-center text-purple-200 py-8">Memuat data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <script src="<?php echo e(asset('js/auth.js')); ?>"></script>
    <script src="<?php echo e(asset('js/utils.js')); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const auth = window.auth;
            const Utils = window.Utils;

            // Check authentication and role
            if (!auth.isAuthenticated()) {
                window.location.href = '/';
                return;
            }

            // Load guru dashboard data
            try {
                const response = await Utils.apiCall('/dashboard/role-data');
                
                if (response.success) {
                    displayGuruData(response.data);
                } else {
                    Utils.showNotification('Gagal memuat data dashboard', 'error');
                }
            } catch (error) {
                console.error('Failed to load guru data:', error);
                Utils.showNotification('Terjadi kesalahan saat memuat data', 'error');
            }

            function displayGuruData(data) {
                const { stats, students, recent_assessments } = data;

                // Update stats
                document.getElementById('total-students').textContent = stats.total_students || 0;
                document.getElementById('monthly-assessments').textContent = stats.assessments_this_month || 0;
                document.getElementById('students-attention').textContent = stats.students_need_attention || 0;

                // Display students
                displayStudents(students);

                // Update recent assessments table
                displayRecentAssessments(recent_assessments);
            }

            function displayStudents(students) {
                const studentsGrid = document.getElementById('students-grid');
                
                if (students && students.length > 0) {
                    studentsGrid.innerHTML = students.map(student => `
                        <div class="bg-white/10 p-4 rounded-lg">
                            <div class="flex items-center mb-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">${student.first_name.charAt(0)}</span>
                                </div>
                                <div>
                                    <h4 class="text-white font-medium">${student.first_name} ${student.last_name}</h4>
                                    <p class="text-purple-200 text-sm">Kelas ${student.class || '-'}</p>
                                </div>
                            </div>
                            <div class="text-xs text-purple-300">
                                <p>NIS: ${student.student_id || '-'}</p>
                                <p>Gender: ${student.gender === 'L' ? 'Laki-laki' : student.gender === 'P' ? 'Perempuan' : '-'}</p>
                            </div>
                        </div>
                    `).join('');
                } else {
                    studentsGrid.innerHTML = '<div class="text-center text-purple-200 py-8">Belum ada data siswa</div>';
                }
            }

            function displayRecentAssessments(assessments) {
                const tbody = document.getElementById('recent-assessments');
                
                if (assessments && assessments.length > 0) {
                    tbody.innerHTML = assessments.map(assessment => {
                        const statusColors = {
                            'normal': 'bg-green-500/20 text-green-300',
                            'concern': 'bg-yellow-500/20 text-yellow-300',
                            'high_risk': 'bg-red-500/20 text-red-300',
                            'severe': 'bg-red-600/20 text-red-200'
                        };

                        return `
                            <tr class="border-b border-white/10">
                                <td class="py-3 text-white">${assessment.user.first_name} ${assessment.user.last_name}</td>
                                <td class="py-3 text-purple-200">${assessment.user.class || '-'}</td>
                                <td class="py-3 text-purple-200">${assessment.form_template.name}</td>
                                <td class="py-3 text-white">${assessment.total_score}</td>
                                <td class="py-3">
                                    <span class="px-2 py-1 rounded-full text-xs ${statusColors[assessment.status] || 'bg-gray-500/20 text-gray-300'}">
                                        ${assessment.status}
                                    </span>
                                </td>
                                <td class="py-3 text-purple-200">${new Date(assessment.created_at).toLocaleDateString('id-ID')}</td>
                            </tr>
                        `;
                    }).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="6" class="text-center text-purple-200 py-8">Belum ada assessment</td></tr>';
                }
            }

            // Action functions
            window.showStudentsList = () => {
                Utils.showNotification('Fitur daftar siswa akan segera tersedia', 'info');
            };

            window.showClassReport = () => {
                Utils.showNotification('Fitur laporan kelas akan segera tersedia', 'info');
            };

            // Logout function
            window.logout = async () => {
                try {
                    await auth.logout();
                    window.location.href = '/';
                } catch (error) {
                    console.error('Logout failed:', error);
                }
            };
        });
    </script>

    <!-- Modern JavaScript Framework -->
    <script src="<?php echo e(asset('js/auth.js')); ?>"></script>
    <script src="<?php echo e(asset('js/utils.js')); ?>"></script>
    <script src="<?php echo e(asset('js/modern-components.js')); ?>"></script>
    <script src="<?php echo e(asset('js/performance-optimizer.js')); ?>"></script>
    <script src="<?php echo e(asset('js/modern-dashboard.js')); ?>"></script>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize modern dashboard
        document.addEventListener('DOMContentLoaded', () => {
            if (window.modernDashboard) {
                window.modernDashboard.init();
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\santrimental1\backend\resources\views/guru-dashboard.blade.php ENDPATH**/ ?>