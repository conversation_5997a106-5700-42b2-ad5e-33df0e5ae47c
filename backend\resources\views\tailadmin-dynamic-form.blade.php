@extends('tailadmin-base')

@php
    $assessmentTitles = [
        'SRQ20' => 'SRQ-20: Self Reporting Questionnaire',
        'DASS42' => 'DASS-42: Depression Anxiety Stress Scale',
        'GSE' => 'GSE: General Self-Efficacy Scale',
        'MHKQ' => 'MHKQ: Mental Health Knowledge Questionnaire',
        'MSCS' => 'MSCS: Multidimensional Self-Concept Scale',
        'PDD' => 'Asesmen PDD: Perceived Devaluation-Discrimination'
    ];

    $assessmentDescriptions = [
        'SRQ20' => 'Skrining gangguan mental umum dengan 20 pertanyaan sederhana',
        'DASS42' => 'Penilaian tingkat depresi, kecema<PERSON>, dan stres dengan 42 item',
        'GSE' => 'Evaluasi keyakinan diri dalam menghadapi berbagai situasi',
        'MHKQ' => 'Pengukuran pengetahuan tentang kesehatan mental',
        'MSCS' => 'Penilaian konsep diri multidimensional',
        'PDD' => 'Pengukuran persepsi stigma dan diskriminasi terkait kesehatan mental'
    ];

    $currentTitle = $assessmentTitles[$code] ?? "Assessment {$code}";
    $currentDescription = $assessmentDescriptions[$code] ?? 'Jawab semua pertanyaan dengan jujur untuk mendapatkan hasil yang akurat';
@endphp

@section('title', "{$currentTitle} - SantriMental")
@section('dashboard-subtitle', 'Assessment Center')
@section('page-title', $currentTitle)
@section('page-description', $currentDescription)
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Menu Utama</p>

    <a href="{{ route('dashboard') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Assessment</span>
        <div class="ml-auto">
            <span class="tailadmin-badge warning text-xs">Aktif</span>
        </div>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Riwayat Tes</span>
    </a>
</div>
@endsection

@section('header-actions')
<!-- Assessment Type Badge -->
<div class="hidden md:flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-primary-100 to-primary-50 dark:from-primary-900 dark:to-primary-800 rounded-lg border border-primary-200 dark:border-primary-700">
    <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
    <span class="text-primary-700 dark:text-primary-300 text-sm font-medium">{{ $code }}</span>
</div>

<!-- Progress Indicator -->
<div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-warning-100 dark:bg-warning-900 rounded-lg border border-warning-200 dark:border-warning-800">
    <div class="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>
    <span class="text-warning-600 dark:text-warning-400 text-sm font-medium">Assessment Berlangsung</span>
</div>

<!-- Time Indicator -->
<div class="hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <span class="text-gray-600 dark:text-gray-400 text-sm font-medium" id="timer-display">00:00</span>
</div>

<!-- Save & Exit -->
<button class="tailadmin-btn tailadmin-btn-outline" onclick="saveAndExit()">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
    </svg>
    <span class="hidden sm:inline">Simpan &</span> Keluar
</button>

<!-- Back to Assessments -->
<button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.href='{{ route('assessments') }}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    <span class="hidden sm:inline">Kembali ke</span> Assessment
</button>
@endsection

@section('breadcrumb')
<a href="{{ route('dashboard') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Dashboard</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<a href="{{ route('assessments') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Pilih Assessment</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">{{ $currentTitle }}</span>
@endsection

@section('content')
<!-- Assessment Header -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="text-center">
        <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-3xl font-bold" id="assessment-icon">📋</span>
        </div>
        <h2 class="text-3xl font-bold tailadmin-text-primary mb-3" id="assessment-title">{{ $currentTitle }}</h2>
        <p class="tailadmin-text-secondary text-lg mb-6 max-w-2xl mx-auto" id="assessment-description">{{ $currentDescription }}</p>

        <!-- Assessment Info -->
        <div class="flex flex-wrap justify-center gap-4 mb-6">
            <div class="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="text-gray-700 dark:text-gray-300 text-sm font-medium" id="assessment-date">{{ date('d M Y') }}</span>
            </div>

            <div class="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-gray-700 dark:text-gray-300 text-sm font-medium" id="assessment-time">Estimasi: <span id="time-estimate">15-20</span> menit</span>
            </div>

            <div class="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
                <span class="text-gray-700 dark:text-gray-300 text-sm font-medium" id="question-count">Total: <span id="total-questions">0</span> pertanyaan</span>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-xl mx-auto">
            <div class="flex items-center justify-between text-sm tailadmin-text-secondary mb-2">
                <span>Progress <span id="current-question">0</span>/<span id="total-questions-progress">0</span></span>
                <span id="progress-text">0%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-dark-300 rounded-full h-3 overflow-hidden">
                <div id="progress-bar" class="bg-primary-500 h-3 rounded-full transition-all duration-300 flex items-center justify-end" style="width: 0%">
                    <div class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assessment Form -->
<div class="tailadmin-card mb-8">
    <!-- Loading State -->
    <div id="loading-state" class="text-center py-12">
        <div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="tailadmin-text-secondary">Memuat pertanyaan assessment...</p>
    </div>

    <!-- Form Content -->
    <div id="form-content" class="hidden">
        <form id="assessment-form">
            <div id="questions-container">
                <!-- Questions will be inserted here -->
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-dark-300">
                <button type="button" id="prev-btn" class="tailadmin-btn tailadmin-btn-outline" disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Sebelumnya
                </button>
                
                <div class="flex items-center space-x-2">
                    <span class="text-sm tailadmin-text-secondary">Pertanyaan</span>
                    <span id="current-question" class="font-semibold tailadmin-text-primary">1</span>
                    <span class="tailadmin-text-secondary">dari</span>
                    <span id="total-questions" class="font-semibold tailadmin-text-primary">20</span>
                </div>
                
                <button type="button" id="next-btn" class="tailadmin-btn tailadmin-btn-primary">
                    Selanjutnya
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button type="submit" id="submit-btn" class="tailadmin-btn tailadmin-btn-success hidden">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Selesai
                </button>
            </div>
        </form>
    </div>

    <!-- Error State -->
    <div id="error-state" class="hidden text-center py-12">
        <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Gagal Memuat Assessment</h3>
        <p class="tailadmin-text-secondary mb-4">Terjadi kesalahan saat memuat pertanyaan assessment</p>
        <button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.reload()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Coba Lagi
        </button>
    </div>
</div>

<!-- Instructions -->
<div class="tailadmin-card">
    <div class="tailadmin-card-header">
        <h3 class="tailadmin-card-title">Petunjuk Pengisian</h3>
    </div>
    <div class="space-y-3 text-sm tailadmin-text-secondary" id="assessment-instructions">
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">1</span>
            </div>
            <p>Jawab semua pertanyaan dengan jujur sesuai dengan kondisi Anda saat ini</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">2</span>
            </div>
            <p>Tidak ada jawaban yang benar atau salah, pilih yang paling sesuai</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">3</span>
            </div>
            <p>Gunakan tombol panah atau angka untuk navigasi cepat</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">4</span>
            </div>
            <p>Progress otomatis disimpan setiap 30 detik</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">5</span>
            </div>
            <p>Hasil assessment akan tersedia segera setelah selesai</p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Form Configuration and Engine -->
<script src="{{ asset('js/utils.js') }}"></script>
<script src="{{ asset('js/form-configs.js') }}"></script>
<script src="{{ asset('js/enhanced-form-engine.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', async () => {
    const assessmentCode = '{{ $code }}';

    try {
        // Initialize enhanced form engine
        window.formEngine = new EnhancedFormEngine(assessmentCode);

        // Initialize timer and progress
        initializeTimer();
        updateAssessmentInfo();

        // Show form content
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('form-content').classList.remove('hidden');

        // Show success message
        setTimeout(() => {
            if (window.showToast) {
                showToast(`Assessment ${assessmentCode} berhasil dimuat`, 'success');
            }
        }, 500);

    } catch (error) {
        console.error('Failed to initialize form engine:', error);
        showError();

        if (window.showToast) {
            showToast('Gagal memuat assessment. Silakan refresh halaman.', 'error');
        }
    }

    function showError() {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('form-content').classList.add('hidden');
        document.getElementById('error-state').classList.remove('hidden');
    }

    // Global functions for backward compatibility
    window.updateAnswer = function(questionId, value) {
        if (window.formEngine) {
            window.formEngine.updateAnswer(questionId, value);
        }
    };

    // Save and exit function
    window.saveAndExit = function() {
        if (window.formEngine) {
            window.formEngine.saveProgress();
            if (window.showToast) {
                showToast('Progress disimpan. Anda dapat melanjutkan nanti.', 'success');
            }
            setTimeout(() => {
                window.location.href = '{{ route("assessments") }}';
            }, 1000);
        }
    };

    // Timer functionality
    let startTime = Date.now();
    let timerInterval;

    function initializeTimer() {
        startTime = Date.now();
        timerInterval = setInterval(updateTimer, 1000);
    }

    function updateTimer() {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const timerDisplay = document.getElementById('timer-display');
        if (timerDisplay) {
            timerDisplay.textContent = timeString;
        }
    }

    function updateAssessmentInfo() {
        const config = formConfigs[assessmentCode];
        if (!config) return;

        // Update time estimate
        if (config.time_limit) {
            const timeEstimate = document.getElementById('time-estimate');
            if (timeEstimate) {
                timeEstimate.textContent = `${config.time_limit-5}-${config.time_limit+5}`;
            }
        }

        // Update question count
        const totalQuestions = config.questions ? config.questions.length : 0;
        const totalQuestionsEl = document.getElementById('total-questions');
        const totalQuestionsProgressEl = document.getElementById('total-questions-progress');

        if (totalQuestionsEl) totalQuestionsEl.textContent = totalQuestions;
        if (totalQuestionsProgressEl) totalQuestionsProgressEl.textContent = totalQuestions;

        // Set appropriate icon
        const iconEl = document.getElementById('assessment-icon');
        if (iconEl) {
            if (config.category === 'mental_health') {
                iconEl.textContent = '🧠';
            } else if (config.category === 'developmental') {
                iconEl.textContent = '👶';
            } else if (config.category === 'educational') {
                iconEl.textContent = '📚';
            } else if (config.category === 'self_efficacy') {
                iconEl.textContent = '💪';
            } else if (config.category === 'mental_health_stigma') {
                iconEl.textContent = '🤝';
            } else {
                iconEl.textContent = '📋';
            }
        }
    }

    // Cleanup timer on page unload
    window.addEventListener('beforeunload', () => {
        if (timerInterval) {
            clearInterval(timerInterval);
        }
    });
});
</script>
@endpush
