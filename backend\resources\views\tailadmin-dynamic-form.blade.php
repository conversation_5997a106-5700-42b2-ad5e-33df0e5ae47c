@extends('tailadmin-base')

@section('title', 'Assessment {{ $code }} - SantriMental')
@section('dashboard-subtitle', 'Assessment Form')
@section('page-title', 'Assessment {{ $code }}')
@section('page-description', '<PERSON><PERSON><PERSON> semua per<PERSON>aan dengan jujur untuk mendapatkan hasil yang akurat')
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Menu Utama</p>

    <a href="{{ route('dashboard') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Assessment</span>
        <div class="ml-auto">
            <span class="tailadmin-badge warning text-xs">Aktif</span>
        </div>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Riwayat Tes</span>
    </a>
</div>
@endsection

@section('header-actions')
<!-- Progress Indicator -->
<div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-primary-100 dark:bg-primary-900 rounded-lg border border-primary-200 dark:border-primary-800">
    <div class="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
    <span class="text-primary-600 dark:text-primary-400 text-sm font-medium">Assessment Berlangsung</span>
</div>

<!-- Save & Exit -->
<button class="tailadmin-btn tailadmin-btn-outline" onclick="saveAndExit()">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
    </svg>
    Simpan & Keluar
</button>
@endsection

@section('breadcrumb')
<a href="{{ route('dashboard') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Dashboard</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<a href="{{ route('assessments') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Assessment</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">{{ $code }}</span>
@endsection

@section('content')
<!-- Assessment Header -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-2xl font-bold" id="assessment-icon">📋</span>
        </div>
        <h2 class="text-3xl font-bold tailadmin-text-primary mb-2" id="assessment-title">Loading...</h2>
        <p class="tailadmin-text-secondary text-lg mb-4" id="assessment-description">Memuat informasi assessment...</p>
        
        <!-- Progress Bar -->
        <div class="max-w-md mx-auto">
            <div class="flex items-center justify-between text-sm tailadmin-text-secondary mb-2">
                <span>Progress</span>
                <span id="progress-text">0%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-dark-300 rounded-full h-2">
                <div id="progress-bar" class="bg-primary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Assessment Form -->
<div class="tailadmin-card mb-8">
    <!-- Loading State -->
    <div id="loading-state" class="text-center py-12">
        <div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="tailadmin-text-secondary">Memuat pertanyaan assessment...</p>
    </div>

    <!-- Form Content -->
    <div id="form-content" class="hidden">
        <form id="assessment-form">
            <div id="questions-container">
                <!-- Questions will be inserted here -->
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-dark-300">
                <button type="button" id="prev-btn" class="tailadmin-btn tailadmin-btn-outline" disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Sebelumnya
                </button>
                
                <div class="flex items-center space-x-2">
                    <span class="text-sm tailadmin-text-secondary">Pertanyaan</span>
                    <span id="current-question" class="font-semibold tailadmin-text-primary">1</span>
                    <span class="tailadmin-text-secondary">dari</span>
                    <span id="total-questions" class="font-semibold tailadmin-text-primary">20</span>
                </div>
                
                <button type="button" id="next-btn" class="tailadmin-btn tailadmin-btn-primary">
                    Selanjutnya
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button type="submit" id="submit-btn" class="tailadmin-btn tailadmin-btn-success hidden">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Selesai
                </button>
            </div>
        </form>
    </div>

    <!-- Error State -->
    <div id="error-state" class="hidden text-center py-12">
        <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Gagal Memuat Assessment</h3>
        <p class="tailadmin-text-secondary mb-4">Terjadi kesalahan saat memuat pertanyaan assessment</p>
        <button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.reload()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Coba Lagi
        </button>
    </div>
</div>

<!-- Instructions -->
<div class="tailadmin-card">
    <div class="tailadmin-card-header">
        <h3 class="tailadmin-card-title">Petunjuk Pengisian</h3>
    </div>
    <div class="space-y-3 text-sm tailadmin-text-secondary" id="assessment-instructions">
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">1</span>
            </div>
            <p>Jawab semua pertanyaan dengan jujur sesuai dengan kondisi Anda saat ini</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">2</span>
            </div>
            <p>Tidak ada jawaban yang benar atau salah, pilih yang paling sesuai</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">3</span>
            </div>
            <p>Gunakan tombol panah atau angka untuk navigasi cepat</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">4</span>
            </div>
            <p>Progress otomatis disimpan setiap 30 detik</p>
        </div>
        <div class="flex items-start">
            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span class="text-primary-500 text-xs font-bold">5</span>
            </div>
            <p>Hasil assessment akan tersedia segera setelah selesai</p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Form Configuration and Engine -->
<script src="{{ asset('js/utils.js') }}"></script>
<script src="{{ asset('js/form-configs.js') }}"></script>
<script src="{{ asset('js/enhanced-form-engine.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', async () => {
    const assessmentCode = '{{ $code }}';

    try {
        // Initialize enhanced form engine
        window.formEngine = new EnhancedFormEngine(assessmentCode);

        // Show form content
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('form-content').classList.remove('hidden');

        // Show success message
        setTimeout(() => {
            if (window.showToast) {
                showToast(`Assessment ${assessmentCode} berhasil dimuat`, 'success');
            }
        }, 500);

    } catch (error) {
        console.error('Failed to initialize form engine:', error);
        showError();

        if (window.showToast) {
            showToast('Gagal memuat assessment. Silakan refresh halaman.', 'error');
        }
    }

    function showError() {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('form-content').classList.add('hidden');
        document.getElementById('error-state').classList.remove('hidden');
    }

    // Global functions for backward compatibility
    window.updateAnswer = function(questionId, value) {
        if (window.formEngine) {
            window.formEngine.updateAnswer(questionId, value);
        }
    };

    // Save and exit function
    window.saveAndExit = function() {
        if (window.formEngine) {
            window.formEngine.saveProgress();
            if (window.showToast) {
                showToast('Progress disimpan. Anda dapat melanjutkan nanti.', 'success');
            }
            setTimeout(() => {
                window.location.href = '{{ route("assessments") }}';
            }, 1000);
        }
    };
});
</script>
@endpush
