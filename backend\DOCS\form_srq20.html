<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner SRQ-20</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom styles for better radio button appearance */
        .custom-radio-label {
            transition: all 0.2s ease-in-out;
        }
        input[type="radio"]:checked + .custom-radio-label {
            border-color: #3b82f6; /* blue-500 */
            background-color: #dbeafe; /* blue-100 */
            color: #2563eb; /* blue-600 */
            font-weight: 600;
        }
        .result-card {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200 flex items-center justify-center min-h-screen p-4">

    <main class="bg-white dark:bg-slate-800 w-full max-w-3xl rounded-2xl shadow-2xl p-6 sm:p-8 md:p-10 transition-all duration-300">
        
        <!-- Form Section -->
        <div id="form-section">
            <div class="text-center mb-8">
                <h1 class="text-2xl sm:text-3xl font-bold text-blue-600 dark:text-blue-500">Kuesioner Kesehatan Jiwa (SRQ-20)</h1>
                <p class="text-slate-500 dark:text-slate-400 mt-2 max-w-xl mx-auto">Jawablah pertanyaan berikut berdasarkan apa yang Anda rasakan dalam <strong>30 hari terakhir</strong>.</p>
            </div>

            <div id="alert-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-6" role="alert">
                <strong class="font-bold">Perhatian!</strong>
                <span class="block sm:inline">Harap jawab semua pertanyaan sebelum melihat hasil.</span>
            </div>

            <form id="srqForm">
                <div id="questions-container" class="space-y-6">
                    <!-- Questions will be dynamically inserted here -->
                </div>

                <div class="mt-10 text-center">
                    <button type="submit" class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800">
                        Lihat Hasil
                    </button>
                </div>
            </form>
        </div>

        <!-- Result Section -->
        <div id="result-section" class="hidden text-center result-card">
            <h2 class="text-2xl font-bold mb-4">Hasil Kuesioner Anda</h2>
            
            <div id="result-content" class="p-6 rounded-lg mb-6">
                <p class="text-lg text-slate-600 dark:text-slate-300">Total Skor "Ya" Anda:</p>
                <p id="score" class="text-6xl font-bold my-2"></p>
                <p id="interpretation" class="text-lg font-semibold"></p>
                <p id="recommendation" class="text-slate-500 dark:text-slate-400 mt-4 max-w-md mx-auto"></p>
            </div>
            
            <button id="reset-button" class="w-full sm:w-auto bg-slate-600 hover:bg-slate-700 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-slate-300 dark:focus:ring-slate-800">
                Ulangi Kuesioner
            </button>
        </div>

    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const srqForm = document.getElementById('srqForm');
            const questionsContainer = document.getElementById('questions-container');
            const formSection = document.getElementById('form-section');
            const resultSection = document.getElementById('result-section');
            const scoreEl = document.getElementById('score');
            const interpretationEl = document.getElementById('interpretation');
            const recommendationEl = document.getElementById('recommendation');
            const resultContentEl = document.getElementById('result-content');
            const resetButton = document.getElementById('reset-button');
            const alertMessage = document.getElementById('alert-message');

            const questions = [
                "Apakah Anda sering merasa sakit kepala?",
                "Apakah nafsu makan Anda buruk?",
                "Apakah Anda tidur tidak nyenyak?",
                "Apakah Anda mudah merasa takut?",
                "Apakah Anda merasa cemas, tegang, atau khawatir?",
                "Apakah tangan Anda gemetar?",
                "Apakah pencernaan Anda terganggu atau buruk?",
                "Apakah Anda sulit berpikir jernih?",
                "Apakah Anda merasa tidak bahagia?",
                "Apakah Anda lebih sering menangis?",
                "Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?",
                "Apakah Anda mengalami kesulitan dalam mengambil keputusan?",
                "Apakah pekerjaan sehari-hari Anda terganggu?",
                "Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?",
                "Apakah Anda kehilangan minat pada berbagai hal?",
                "Apakah Anda merasa tidak berharga?",
                "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?",
                "Apakah Anda merasa lelah sepanjang waktu?",
                "Apakah Anda merasa tidak enak di perut?",
                "Apakah Anda mudah lelah?"
            ];

            // Function to generate question elements
            function renderQuestions() {
                let questionsHTML = '';
                questions.forEach((q, index) => {
                    const questionNumber = index + 1;
                    questionsHTML += `
                        <div class="p-4 border border-slate-200 dark:border-slate-700 rounded-lg transition-all duration-300 hover:shadow-md hover:border-blue-500 dark:hover:border-blue-500">
                            <p class="text-slate-700 dark:text-slate-300 mb-3 font-medium">${questionNumber}. ${q}</p>
                            <div class="flex items-center justify-end space-x-4">
                                <input type="radio" id="q${questionNumber}-yes" name="q${questionNumber}" value="1" class="sr-only" required>
                                <label for="q${questionNumber}-yes" class="custom-radio-label cursor-pointer w-24 text-center py-2 px-4 border-2 border-slate-300 dark:border-slate-600 rounded-lg">Ya</label>
                                
                                <input type="radio" id="q${questionNumber}-no" name="q${questionNumber}" value="0" class="sr-only" required>
                                <label for="q${questionNumber}-no" class="custom-radio-label cursor-pointer w-24 text-center py-2 px-4 border-2 border-slate-300 dark:border-slate-600 rounded-lg">Tidak</label>
                            </div>
                        </div>
                    `;
                });
                questionsContainer.innerHTML = questionsHTML;
            }
            
            renderQuestions();

            // Handle form submission
            srqForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                const formData = new FormData(srqForm);
                const answeredQuestions = Array.from(formData.keys()).length;

                // Check if all questions are answered
                if (answeredQuestions < questions.length) {
                    alertMessage.classList.remove('hidden');
                    window.scrollTo(0, 0);
                    return;
                }
                
                alertMessage.classList.add('hidden');

                let score = 0;
                for (let value of formData.values()) {
                    score += parseInt(value);
                }

                displayResult(score);
            });

            // Function to display the result
            function displayResult(score) {
                scoreEl.textContent = score;

                // Remove previous color classes
                resultContentEl.classList.remove('bg-green-100', 'dark:bg-green-900/50', 'bg-yellow-100', 'dark:bg-yellow-900/50');
                scoreEl.classList.remove('text-green-600', 'dark:text-green-400', 'text-yellow-600', 'dark:text-yellow-400');
                interpretationEl.classList.remove('text-green-800', 'dark:text-green-300', 'text-yellow-800', 'dark:text-yellow-300');


                if (score > 6) {
                    resultContentEl.classList.add('bg-yellow-100', 'dark:bg-yellow-900/50');
                    scoreEl.classList.add('text-yellow-600', 'dark:text-yellow-400');
                    interpretationEl.classList.add('text-yellow-800', 'dark:text-yellow-300');
                    interpretationEl.textContent = "Terindikasi Mengalami Gangguan Emosional";
                    recommendationEl.textContent = "Skor Anda menunjukkan adanya kemungkinan masalah kesehatan jiwa. Disarankan untuk berkonsultasi dengan profesional seperti psikolog atau psikiater untuk evaluasi lebih lanjut.";
                } else {
                    resultContentEl.classList.add('bg-green-100', 'dark:bg-green-900/50');
                    scoreEl.classList.add('text-green-600', 'dark:text-green-400');
                    interpretationEl.classList.add('text-green-800', 'dark:text-green-300');
                    interpretationEl.textContent = "Kondisi Emosional Anda Cukup Baik";
                    recommendationEl.textContent = "Skor Anda berada dalam rentang normal. Terus pertahankan gaya hidup sehat dan kelola stres dengan baik untuk menjaga kesehatan mental Anda.";
                }

                formSection.classList.add('hidden');
                resultSection.classList.remove('hidden');
            }

            // Handle reset button click
            resetButton.addEventListener('click', () => {
                srqForm.reset();
                resultSection.classList.add('hidden');
                formSection.classList.remove('hidden');
                window.scrollTo(0, 0);
            });
        });
    </script>
</body>
</html>
