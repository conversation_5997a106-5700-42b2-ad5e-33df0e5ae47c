/**
 * Enhanced Form Engine for All Assessment Types
 * Handles SRQ-20, DASS-42, GSE, MSCS, MHKQ with optimal performance
 */

class EnhancedFormEngine {
    constructor(assessmentCode) {
        this.assessmentCode = assessmentCode;
        this.config = FormConfigs[assessmentCode];
        this.currentQuestionIndex = 0;
        this.answers = {};
        this.startTime = Date.now();
        this.questionStartTime = Date.now();
        this.timeSpentPerQuestion = {};
        this.isSubmitting = false;
        
        if (!this.config) {
            throw new Error(`Assessment configuration not found for: ${assessmentCode}`);
        }
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSavedProgress();
        this.renderAssessmentInfo();
        this.renderCurrentQuestion();
        this.startAutoSave();
    }

    setupEventListeners() {
        // Navigation buttons
        document.getElementById('prev-btn')?.addEventListener('click', () => this.previousQuestion());
        document.getElementById('next-btn')?.addEventListener('click', () => this.nextQuestion());
        document.getElementById('submit-btn')?.addEventListener('click', () => this.submitAssessment());
        
        // Auto-save on answer change
        document.addEventListener('change', (e) => {
            if (e.target.name && e.target.name.startsWith('question_')) {
                this.handleAnswerChange(e);
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousQuestion();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.nextQuestion();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                    if (this.config.scoring_rules.type !== 'binary_sum') {
                        e.preventDefault();
                        this.selectAnswer(parseInt(e.key) - 1);
                    }
                    break;
            }
        });

        // Prevent accidental page leave
        window.addEventListener('beforeunload', (e) => {
            if (Object.keys(this.answers).length > 0 && !this.isSubmitting) {
                e.preventDefault();
                e.returnValue = 'Anda memiliki jawaban yang belum disimpan. Yakin ingin keluar?';
            }
        });
    }

    renderAssessmentInfo() {
        // Update header information
        document.getElementById('assessment-title').textContent = this.config.name;
        document.getElementById('assessment-description').textContent = this.config.description;
        document.getElementById('total-questions').textContent = this.config.questions.length;
        
        // Update instructions
        const instructionsEl = document.getElementById('assessment-instructions');
        if (instructionsEl) {
            instructionsEl.textContent = this.config.instructions;
        }

        // Set assessment icon based on category
        const iconEl = document.getElementById('assessment-icon');
        if (iconEl) {
            const icons = {
                'mental_health': '🧠',
                'psychological': '📊',
                'self_efficacy': '💪',
                'cultural_sensitivity': '🤝',
                'knowledge': '📚',
                'developmental': '👶'
            };
            iconEl.textContent = icons[this.config.category] || '📋';
        }
    }

    renderCurrentQuestion() {
        const question = this.config.questions[this.currentQuestionIndex];
        if (!question) return;

        const questionId = this.currentQuestionIndex + 1;
        const container = document.getElementById('questions-container');
        
        // Track time spent on previous question
        if (this.currentQuestionIndex > 0) {
            const prevQuestionId = this.currentQuestionIndex;
            this.timeSpentPerQuestion[prevQuestionId] = Date.now() - this.questionStartTime;
        }
        this.questionStartTime = Date.now();

        // Generate options based on assessment type
        const options = this.generateOptions();
        
        const questionHTML = `
            <div class="tailadmin-card border-l-4 border-primary-500 animate-fade-in">
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold tailadmin-text-primary">
                            ${questionId}. ${question}
                        </h3>
                        <div class="text-sm tailadmin-text-secondary">
                            <span class="font-medium">${this.currentQuestionIndex + 1}</span> / ${this.config.questions.length}
                        </div>
                    </div>
                    
                    <div class="space-y-3" id="options-container">
                        ${options.map((option, index) => this.renderOption(questionId, option, index)).join('')}
                    </div>
                    
                    ${this.renderQuestionHelp()}
                </div>
            </div>
        `;

        container.innerHTML = questionHTML;
        this.updateProgress();
        this.updateNavigation();
        this.focusFirstOption();
    }

    generateOptions() {
        const type = this.config.scoring_rules.type;
        
        switch(type) {
            case 'binary_sum':
                return [
                    { value: 0, label: 'Tidak', color: 'success' },
                    { value: 1, label: 'Ya', color: 'warning' }
                ];
            
            case 'dass_scale':
                return [
                    { value: 0, label: 'Tidak sesuai sama sekali', color: 'success' },
                    { value: 1, label: 'Kadang-kadang', color: 'warning' },
                    { value: 2, label: 'Lumayan sering', color: 'danger' },
                    { value: 3, label: 'Sering sekali', color: 'danger' }
                ];
            
            case 'likert_sum':
                const scale = this.config.scoring_rules.scale;
                if (scale.max === 4) {
                    return [
                        { value: 1, label: 'Tidak benar sama sekali', color: 'danger' },
                        { value: 2, label: 'Hampir tidak benar', color: 'warning' },
                        { value: 3, label: 'Agak benar', color: 'primary' },
                        { value: 4, label: 'Benar sekali', color: 'success' }
                    ];
                } else if (scale.max === 7) {
                    return [
                        { value: 1, label: 'Sangat tidak setuju', color: 'danger' },
                        { value: 2, label: 'Tidak setuju', color: 'warning' },
                        { value: 3, label: 'Agak tidak setuju', color: 'secondary' },
                        { value: 4, label: 'Netral', color: 'primary' },
                        { value: 5, label: 'Agak setuju', color: 'primary' },
                        { value: 6, label: 'Setuju', color: 'success' },
                        { value: 7, label: 'Sangat setuju', color: 'success' }
                    ];
                }
                break;
            
            case 'knowledge_sum':
                return [
                    { value: 'benar', label: 'Benar', color: 'success' },
                    { value: 'salah', label: 'Salah', color: 'danger' }
                ];

            case 'pdd_screening':
                return [
                    { value: 0, label: 'Tidak', color: 'success' },
                    { value: 1, label: 'Ya', color: 'warning' }
                ];

            default:
                return [
                    { value: 0, label: 'Tidak', color: 'success' },
                    { value: 1, label: 'Ya', color: 'warning' }
                ];
        }
    }

    renderOption(questionId, option, index) {
        const isSelected = this.answers[questionId] == option.value;
        const selectedClass = isSelected ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-900' : '';
        
        return `
            <label class="flex items-center p-4 rounded-lg border border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 cursor-pointer transition-all duration-200 ${selectedClass}" 
                   data-option-index="${index}">
                <input type="radio" 
                       name="question_${questionId}" 
                       value="${option.value}"
                       class="w-4 h-4 text-${option.color}-500 border-gray-300 focus:ring-${option.color}-500"
                       ${isSelected ? 'checked' : ''}
                       onchange="window.formEngine.updateAnswer(${questionId}, '${option.value}')">
                <span class="ml-3 tailadmin-text-primary font-medium">${option.label}</span>
                <div class="ml-auto">
                    <div class="w-3 h-3 bg-${option.color}-500 rounded-full opacity-60"></div>
                </div>
            </label>
        `;
    }

    renderQuestionHelp() {
        const shortcuts = this.config.scoring_rules.type !== 'binary_sum' ? 
            '<p class="text-xs tailadmin-text-secondary mt-4">💡 Tip: Gunakan angka 1-' + this.generateOptions().length + ' untuk memilih jawaban dengan cepat</p>' : 
            '<p class="text-xs tailadmin-text-secondary mt-4">💡 Tip: Gunakan panah kiri/kanan untuk navigasi</p>';
        
        return `
            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-dark-300">
                ${shortcuts}
            </div>
        `;
    }

    updateAnswer(questionId, value) {
        this.answers[questionId] = value;
        this.saveProgress();
        this.updateProgress();
        this.updateNavigation();
        
        // Auto-advance for binary questions after a short delay
        if (this.config.scoring_rules.type === 'binary_sum' || this.config.scoring_rules.type === 'knowledge_sum') {
            setTimeout(() => {
                if (this.currentQuestionIndex < this.config.questions.length - 1) {
                    this.nextQuestion();
                }
            }, 500);
        }
    }

    selectAnswer(optionIndex) {
        const options = this.generateOptions();
        if (optionIndex < options.length) {
            const questionId = this.currentQuestionIndex + 1;
            const value = options[optionIndex].value;
            
            // Update radio button
            const radio = document.querySelector(`input[name="question_${questionId}"][value="${value}"]`);
            if (radio) {
                radio.checked = true;
                this.updateAnswer(questionId, value);
            }
        }
    }

    previousQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.renderCurrentQuestion();
        }
    }

    nextQuestion() {
        const questionId = this.currentQuestionIndex + 1;
        
        // Validate current question is answered
        if (!this.answers[questionId]) {
            this.showToast('Silakan jawab pertanyaan ini terlebih dahulu', 'warning');
            this.focusFirstOption();
            return;
        }
        
        if (this.currentQuestionIndex < this.config.questions.length - 1) {
            this.currentQuestionIndex++;
            this.renderCurrentQuestion();
        }
    }

    updateProgress() {
        const answeredCount = Object.keys(this.answers).length;
        const progress = (answeredCount / this.config.questions.length) * 100;
        
        document.getElementById('progress-bar').style.width = `${progress}%`;
        document.getElementById('progress-text').textContent = `${Math.round(progress)}%`;
        document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
    }

    updateNavigation() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const submitBtn = document.getElementById('submit-btn');
        
        // Previous button
        prevBtn.disabled = this.currentQuestionIndex === 0;
        
        // Next/Submit button logic
        const isLastQuestion = this.currentQuestionIndex === this.config.questions.length - 1;
        const allAnswered = Object.keys(this.answers).length === this.config.questions.length;
        const currentAnswered = this.answers[this.currentQuestionIndex + 1] !== undefined;
        
        if (isLastQuestion) {
            nextBtn.classList.add('hidden');
            submitBtn.classList.remove('hidden');
            submitBtn.disabled = !allAnswered;
        } else {
            nextBtn.classList.remove('hidden');
            submitBtn.classList.add('hidden');
            nextBtn.disabled = !currentAnswered;
        }
    }

    focusFirstOption() {
        setTimeout(() => {
            const firstOption = document.querySelector('#options-container label');
            if (firstOption) {
                firstOption.focus();
            }
        }, 100);
    }

    async submitAssessment() {
        if (this.isSubmitting) return;
        
        const allAnswered = Object.keys(this.answers).length === this.config.questions.length;
        if (!allAnswered) {
            this.showToast('Silakan jawab semua pertanyaan terlebih dahulu', 'warning');
            return;
        }

        this.isSubmitting = true;
        this.showLoading();
        
        try {
            const score = this.calculateScore();
            const result = await this.saveAssessmentResult(score);
            
            this.showToast('Assessment berhasil diselesaikan!', 'success');
            
            // Clear saved progress
            this.clearSavedProgress();
            
            // Redirect to results
            setTimeout(() => {
                window.location.href = `/assessment-result?code=${this.assessmentCode}&score=${score.total}&subscores=${encodeURIComponent(JSON.stringify(score.subscores || {}))}`;
            }, 1500);
            
        } catch (error) {
            console.error('Submission failed:', error);
            this.showToast('Gagal menyimpan hasil assessment. Silakan coba lagi.', 'error');
            this.isSubmitting = false;
            this.hideLoading();
        }
    }

    calculateScore() {
        const rules = this.config.scoring_rules;
        let totalScore = 0;
        let subscores = {};

        switch(rules.type) {
            case 'binary_sum':
            case 'likert_sum':
                totalScore = Object.values(this.answers).reduce((sum, value) => sum + parseInt(value), 0);
                break;
                
            case 'dass_scale':
                // Calculate subscale scores
                Object.keys(rules.subscales).forEach(subscale => {
                    const items = rules.subscales[subscale].items;
                    const multiplier = rules.subscales[subscale].multiplier;
                    const subscaleScore = items.reduce((sum, itemNum) => {
                        return sum + (parseInt(this.answers[itemNum]) || 0);
                    }, 0) * multiplier;
                    subscores[subscale] = subscaleScore;
                });
                totalScore = Object.values(subscores).reduce((sum, score) => sum + score, 0);
                break;
                
            case 'knowledge_sum':
                totalScore = Object.keys(this.answers).reduce((sum, questionId) => {
                    const correctAnswer = rules.correct_answers[questionId];
                    return sum + (this.answers[questionId] === correctAnswer ? 1 : 0);
                }, 0);
                break;

            case 'pdd_screening':
                // Calculate domain scores for PDD
                Object.keys(rules.domains).forEach(domain => {
                    const items = rules.domains[domain].items;
                    const weight = rules.domains[domain].weight;
                    const domainScore = items.reduce((sum, itemNum) => {
                        return sum + (parseInt(this.answers[itemNum]) || 0);
                    }, 0) * weight;
                    subscores[domain] = domainScore;
                });
                totalScore = Object.values(subscores).reduce((sum, score) => sum + score, 0);
                break;
        }

        return {
            total: totalScore,
            subscores: subscores,
            max_score: rules.max_score || rules.min_score + totalScore,
            interpretation: this.getInterpretation(totalScore, subscores)
        };
    }

    getInterpretation(totalScore, subscores = {}) {
        const thresholds = this.config.scoring_rules.thresholds;
        const scoringType = this.config.scoring_rules.type;

        if (subscores && Object.keys(subscores).length > 0) {
            if (scoringType === 'dass_scale') {
                // DASS-42 interpretation
                const interpretations = {};
                Object.keys(subscores).forEach(subscale => {
                    const score = subscores[subscale];
                    const subscaleThresholds = thresholds[subscale];

                    for (const [level, range] of Object.entries(subscaleThresholds)) {
                        if (score >= range[0] && score <= range[1]) {
                            interpretations[subscale] = level;
                            break;
                        }
                    }
                });
                return interpretations;
            } else if (scoringType === 'pdd_screening') {
                // PDD domain interpretation
                const interpretations = {};
                Object.keys(subscores).forEach(domain => {
                    const score = subscores[domain];
                    // Simple domain interpretation based on score
                    if (score <= 2) {
                        interpretations[domain] = 'low_concern';
                    } else if (score <= 5) {
                        interpretations[domain] = 'moderate_concern';
                    } else {
                        interpretations[domain] = 'high_concern';
                    }
                });
                return interpretations;
            }
        } else {
            // Single score interpretation
            for (const [level, range] of Object.entries(thresholds)) {
                if (totalScore >= range.min && totalScore <= range.max) {
                    return level;
                }
            }
        }

        return 'unknown';
    }

    async saveAssessmentResult(score) {
        const completionTime = Date.now() - this.startTime;
        
        const data = {
            assessment_code: this.assessmentCode,
            answers: this.answers,
            total_score: score.total,
            subscores: score.subscores,
            interpretation: score.interpretation,
            completion_time: completionTime,
            time_per_question: this.timeSpentPerQuestion,
            started_at: new Date(this.startTime).toISOString(),
            completed_at: new Date().toISOString()
        };

        // In production, this would be an API call
        if (Utils.MOCK_MODE) {
            return new Promise(resolve => {
                setTimeout(() => resolve({ success: true, id: Date.now() }), 1000);
            });
        } else {
            return await Utils.apiCall('/assessments', {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }
    }

    saveProgress() {
        const progressData = {
            assessmentCode: this.assessmentCode,
            currentQuestionIndex: this.currentQuestionIndex,
            answers: this.answers,
            startTime: this.startTime,
            timeSpentPerQuestion: this.timeSpentPerQuestion,
            savedAt: Date.now()
        };
        
        localStorage.setItem(`assessment_progress_${this.assessmentCode}`, JSON.stringify(progressData));
    }

    loadSavedProgress() {
        const saved = localStorage.getItem(`assessment_progress_${this.assessmentCode}`);
        if (saved) {
            try {
                const data = JSON.parse(saved);
                
                // Only load if saved within last 24 hours
                if (Date.now() - data.savedAt < 24 * 60 * 60 * 1000) {
                    this.currentQuestionIndex = data.currentQuestionIndex || 0;
                    this.answers = data.answers || {};
                    this.startTime = data.startTime || Date.now();
                    this.timeSpentPerQuestion = data.timeSpentPerQuestion || {};
                    
                    if (Object.keys(this.answers).length > 0) {
                        this.showToast('Progress sebelumnya dimuat', 'info');
                    }
                }
            } catch (error) {
                console.error('Failed to load saved progress:', error);
            }
        }
    }

    clearSavedProgress() {
        localStorage.removeItem(`assessment_progress_${this.assessmentCode}`);
    }

    startAutoSave() {
        setInterval(() => {
            if (Object.keys(this.answers).length > 0) {
                this.saveProgress();
            }
        }, 30000); // Auto-save every 30 seconds
    }

    showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    showLoading() {
        if (window.showLoading) {
            window.showLoading();
        }
    }

    hideLoading() {
        if (window.hideLoading) {
            window.hideLoading();
        }
    }
}

// Export for global use
window.EnhancedFormEngine = EnhancedFormEngine;
