{"name": "illuminate/process", "description": "The Illuminate Process package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0", "symfony/process": "^6.2"}, "autoload": {"psr-4": {"Illuminate\\Process\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}