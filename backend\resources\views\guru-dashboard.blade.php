<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Dashboard Guru - SantriMental</title>

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="{{ asset('css/modern-dashboard.css') }}">

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Modern Dashboard JavaScript -->
    <script src="{{ asset('js/modern-dashboard.js') }}"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
</head>
<body class="gradient-mesh min-h-screen transition-all duration-300">

    <!-- Modern Teacher Header -->
    <nav class="header-modern sticky top-0 z-50">
        <div class="flex items-center justify-between p-6 max-w-7xl mx-auto">
            <div class="flex items-center">
                <div class="w-12 h-12 gradient-success rounded-xl flex items-center justify-center mr-4 shadow-lg">
                    <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-white">SantriMental Guru</h1>
                    <p class="text-white/60 text-sm">Dashboard Monitoring Siswa</p>
                </div>
            </div>

            <!-- Teacher Navigation -->
            <div class="hidden md:flex items-center space-x-6">
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Dashboard</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Siswa</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Laporan</a>
                <a href="#" class="text-white/70 hover:text-white transition-colors font-medium">Kelas</a>
            </div>

            <div class="flex items-center space-x-4">
                <!-- Class Status -->
                <div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-blue-500/20 rounded-lg border border-blue-500/30">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <span class="text-blue-400 text-sm font-medium">Kelas Aktif</span>
                </div>

                <!-- Notifications -->
                <button class="btn-ghost p-3 rounded-xl relative" data-tooltip="Notifikasi">
                    <i data-lucide="bell" class="w-5 h-5"></i>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full"></div>
                </button>

                <button class="btn-ghost p-3 rounded-xl" data-tooltip="Bantuan">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                </button>

                <!-- Teacher Profile -->
                <div class="flex items-center space-x-3 ml-4 pl-4 border-l border-white/20">
                    <div class="text-right">
                        <p class="text-white font-medium text-sm">Pak/Bu Guru</p>
                        <p class="text-white/60 text-xs"><EMAIL></p>
                    </div>
                    <div class="w-10 h-10 gradient-success rounded-xl flex items-center justify-center">
                        <i data-lucide="graduation-cap" class="w-5 h-5 text-white"></i>
                    </div>
                    <button onclick="logout()" class="btn-ghost p-2 rounded-lg text-red-400 hover:bg-red-500/10" data-tooltip="Logout">
                        <i data-lucide="log-out" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-6 py-8 space-y-8">

        <!-- Welcome Banner -->
        <div class="modern-card p-8 animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-16 h-16 gradient-success rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                        <i data-lucide="users" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-white mb-2">Dashboard Monitoring Siswa</h2>
                        <p class="text-white/70 text-lg">Pantau kesehatan mental siswa dengan mudah dan efektif</p>
                        <div class="flex items-center mt-3 space-x-4">
                            <div class="flex items-center text-sm text-green-400">
                                <i data-lucide="eye" class="w-4 h-4 mr-1"></i>
                                <span>Monitoring Real-time</span>
                            </div>
                            <div class="flex items-center text-sm text-blue-400">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-1"></i>
                                <span>Data Terlindungi</span>
                            </div>
                            <div class="flex items-center text-sm text-purple-400">
                                <i data-lucide="trending-up" class="w-4 h-4 mr-1"></i>
                                <span>Analytics Mendalam</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="text-right">
                        <p class="text-white/60 text-sm">Kelas Aktif</p>
                        <p class="text-white font-semibold text-lg">XII IPA 1</p>
                        <p class="text-white/60 text-sm">32 Siswa</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-primary rounded-xl shadow-lg">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                        +5%
                    </div>
                </div>
                <div>
                    <p class="stats-label">Total Siswa</p>
                    <p id="total-students" class="stats-value">32</p>
                    <p class="text-xs text-white/50 mt-1">Siswa di kelas</p>
                </div>
            </div>

            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-success rounded-xl shadow-lg">
                        <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                        +12%
                    </div>
                </div>
                <div>
                    <p class="stats-label">Skrining Selesai</p>
                    <p id="completed-assessments" class="stats-value">28</p>
                    <p class="text-xs text-white/50 mt-1">Bulan ini</p>
                </div>
            </div>

            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-warning rounded-xl shadow-lg">
                        <i data-lucide="alert-triangle" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change neutral">
                        <i data-lucide="minus" class="w-3 h-3 mr-1"></i>
                        0
                    </div>
                </div>
                <div>
                    <p class="stats-label">Perlu Perhatian</p>
                    <p id="attention-needed" class="stats-value">3</p>
                    <p class="text-xs text-white/50 mt-1">Siswa berisiko</p>
                </div>
            </div>

            <div class="stats-card animate-fade-in-up" style="animation-delay: 0.5s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 gradient-secondary rounded-xl shadow-lg">
                        <i data-lucide="heart-pulse" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="stats-change positive">
                        <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                        Baik
                    </div>
                </div>
                <div>
                    <p class="stats-label">Rata-rata Kelas</p>
                    <p id="class-average" class="stats-value">4.2</p>
                    <p class="text-xs text-white/50 mt-1">Skor SRQ-20</p>
                </div>
            </div>
        </div>
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">👨‍🎓</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Assessment Bulan Ini</p>
                        <p id="monthly-assessments" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📋</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Perlu Perhatian</p>
                        <p id="students-attention" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">⚠️</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Daftar Siswa</h3>
                <p class="text-purple-200 text-sm mb-4">Lihat dan kelola data siswa di kelas Anda</p>
                <button onclick="showStudentsList()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Lihat Daftar Siswa
                </button>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Laporan Kelas</h3>
                <p class="text-purple-200 text-sm mb-4">Analisis hasil assessment seluruh kelas</p>
                <button onclick="showClassReport()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors w-full">
                    Lihat Laporan
                </button>
            </div>
        </div>

        <!-- Students List -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <h3 class="text-lg font-semibold text-white mb-4">Daftar Siswa</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="students-grid">
                <div class="text-center text-purple-200 py-8">Memuat data siswa...</div>
            </div>
        </div>

        <!-- Recent Assessments -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Assessment Terbaru</h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-white/20">
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Siswa</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Kelas</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Assessment</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Skor</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Status</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Tanggal</th>
                        </tr>
                    </thead>
                    <tbody id="recent-assessments">
                        <tr>
                            <td colspan="6" class="text-center text-purple-200 py-8">Memuat data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const auth = window.auth;
            const Utils = window.Utils;

            // Check authentication and role
            if (!auth.isAuthenticated()) {
                window.location.href = '/';
                return;
            }

            // Load guru dashboard data
            try {
                const response = await Utils.apiCall('/dashboard/role-data');
                
                if (response.success) {
                    displayGuruData(response.data);
                } else {
                    Utils.showNotification('Gagal memuat data dashboard', 'error');
                }
            } catch (error) {
                console.error('Failed to load guru data:', error);
                Utils.showNotification('Terjadi kesalahan saat memuat data', 'error');
            }

            function displayGuruData(data) {
                const { stats, students, recent_assessments } = data;

                // Update stats
                document.getElementById('total-students').textContent = stats.total_students || 0;
                document.getElementById('monthly-assessments').textContent = stats.assessments_this_month || 0;
                document.getElementById('students-attention').textContent = stats.students_need_attention || 0;

                // Display students
                displayStudents(students);

                // Update recent assessments table
                displayRecentAssessments(recent_assessments);
            }

            function displayStudents(students) {
                const studentsGrid = document.getElementById('students-grid');
                
                if (students && students.length > 0) {
                    studentsGrid.innerHTML = students.map(student => `
                        <div class="bg-white/10 p-4 rounded-lg">
                            <div class="flex items-center mb-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">${student.first_name.charAt(0)}</span>
                                </div>
                                <div>
                                    <h4 class="text-white font-medium">${student.first_name} ${student.last_name}</h4>
                                    <p class="text-purple-200 text-sm">Kelas ${student.class || '-'}</p>
                                </div>
                            </div>
                            <div class="text-xs text-purple-300">
                                <p>NIS: ${student.student_id || '-'}</p>
                                <p>Gender: ${student.gender === 'L' ? 'Laki-laki' : student.gender === 'P' ? 'Perempuan' : '-'}</p>
                            </div>
                        </div>
                    `).join('');
                } else {
                    studentsGrid.innerHTML = '<div class="text-center text-purple-200 py-8">Belum ada data siswa</div>';
                }
            }

            function displayRecentAssessments(assessments) {
                const tbody = document.getElementById('recent-assessments');
                
                if (assessments && assessments.length > 0) {
                    tbody.innerHTML = assessments.map(assessment => {
                        const statusColors = {
                            'normal': 'bg-green-500/20 text-green-300',
                            'concern': 'bg-yellow-500/20 text-yellow-300',
                            'high_risk': 'bg-red-500/20 text-red-300',
                            'severe': 'bg-red-600/20 text-red-200'
                        };

                        return `
                            <tr class="border-b border-white/10">
                                <td class="py-3 text-white">${assessment.user.first_name} ${assessment.user.last_name}</td>
                                <td class="py-3 text-purple-200">${assessment.user.class || '-'}</td>
                                <td class="py-3 text-purple-200">${assessment.form_template.name}</td>
                                <td class="py-3 text-white">${assessment.total_score}</td>
                                <td class="py-3">
                                    <span class="px-2 py-1 rounded-full text-xs ${statusColors[assessment.status] || 'bg-gray-500/20 text-gray-300'}">
                                        ${assessment.status}
                                    </span>
                                </td>
                                <td class="py-3 text-purple-200">${new Date(assessment.created_at).toLocaleDateString('id-ID')}</td>
                            </tr>
                        `;
                    }).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="6" class="text-center text-purple-200 py-8">Belum ada assessment</td></tr>';
                }
            }

            // Action functions
            window.showStudentsList = () => {
                Utils.showNotification('Fitur daftar siswa akan segera tersedia', 'info');
            };

            window.showClassReport = () => {
                Utils.showNotification('Fitur laporan kelas akan segera tersedia', 'info');
            };

            // Logout function
            window.logout = async () => {
                try {
                    await auth.logout();
                    window.location.href = '/';
                } catch (error) {
                    console.error('Logout failed:', error);
                }
            };
        });
    </script>

    <!-- Modern JavaScript Framework -->
    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script src="{{ asset('js/modern-components.js') }}"></script>
    <script src="{{ asset('js/performance-optimizer.js') }}"></script>
    <script src="{{ asset('js/modern-dashboard.js') }}"></script>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize modern dashboard
        document.addEventListener('DOMContentLoaded', () => {
            if (window.modernDashboard) {
                window.modernDashboard.init();
            }
        });
    </script>
</body>
</html>
