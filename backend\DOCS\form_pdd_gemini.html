<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asesmen PDD (Perceived Devaluation-Discrimination)</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Menggunakan font Inter sebagai default */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Styling untuk radio button kustom */
        .scale-radio {
            display: none;
        }
        .scale-label {
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border: 2px solid transparent;
        }
        .scale-radio:checked + .scale-label {
            background-color: #3b82f6; /* bg-blue-500 */
            color: white;
            border-color: #2563eb; /* bg-blue-600 */
            transform: scale(1.05);
        }
        .scale-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        /* Animasi untuk memunculkan elemen */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out forwards;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800 flex items-center justify-center min-h-screen p-4">

    <main class="w-full max-w-2xl mx-auto">
        <div id="app-container" class="bg-white p-6 sm:p-8 rounded-2xl shadow-lg transition-all duration-300">
            
            <!-- Header -->
            <header class="text-center border-b border-slate-200 pb-6 mb-6">
                <h1 class="text-2xl sm:text-3xl font-bold text-slate-900">Asesmen PDD</h1>
                <p class="mt-2 text-slate-600">Perceived Devaluation-Discrimination</p>
            </header>

            <!-- Bagian Intro & Karakteristik -->
            <div id="intro-section" class="fade-in">
                <h2 class="text-lg font-semibold text-slate-800">Selamat Datang</h2>
                <p class="mt-2 text-slate-600">Formulir ini membantu Anda merefleksikan sejauh mana Anda merasa diperlakukan negatif karena karakteristik pribadi tertentu. Jawaban Anda bersifat rahasia.</p>
                
                <div class="mt-6">
                    <label for="characteristic" class="block text-sm font-medium text-slate-700 mb-2">
                        Pertama, sebutkan satu karakteristik atau identitas diri yang akan menjadi fokus asesmen ini:
                    </label>
                    <input type="text" id="characteristic" name="characteristic" class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition" placeholder="Contoh: kondisi kesehatan mental, suku, dll.">
                    <button id="start-btn" class="mt-4 w-full bg-blue-500 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-transform transform hover:scale-105 disabled:bg-slate-300 disabled:cursor-not-allowed disabled:hover:scale-100" disabled>
                        Mulai Asesmen
                    </button>
                </div>
            </div>

            <!-- Bagian Kuesioner (Awalnya tersembunyi) -->
            <div id="assessment-section" class="hidden">
                <!-- Progress Bar -->
                <div class="mb-6">
                    <p class="text-sm text-slate-600 mb-2">Pertanyaan <span id="current-question-number">1</span> dari 12</p>
                    <div class="w-full bg-slate-200 rounded-full h-2.5">
                        <div id="progress-bar" class="bg-blue-500 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Kontainer Pertanyaan -->
                <div id="question-container">
                    <!-- Pertanyaan akan dimuat di sini oleh JavaScript -->
                </div>

                <!-- Tombol Navigasi -->
                <div class="mt-8 flex justify-between items-center">
                    <button id="prev-btn" class="bg-slate-200 text-slate-700 font-semibold py-2 px-6 rounded-lg hover:bg-slate-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-400 transition transform hover:scale-105">Kembali</button>
                    <button id="next-btn" class="bg-blue-500 text-white font-semibold py-2 px-6 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105 disabled:bg-slate-300 disabled:cursor-not-allowed">Lanjut</button>
                </div>
            </div>

            <!-- Bagian Hasil (Awalnya tersembunyi) -->
            <div id="results-section" class="hidden text-center">
                <div id="result-card" class="p-6 rounded-lg">
                    <h2 class="text-2xl font-bold mb-2" id="result-title"></h2>
                    <p class="text-lg mb-4">Total Skor Anda: <span id="total-score" class="font-bold"></span></p>
                    <p class="text-slate-600 leading-relaxed" id="result-interpretation"></p>
                </div>
                <div class="mt-6 border-t border-slate-200 pt-6">
                     <p class="text-sm text-slate-500 mb-4">
                        <strong>Penting:</strong> Formulir ini adalah alat bantu refleksi diri, bukan diagnosis psikologis. Jika hasilnya menimbulkan kekhawatiran, pertimbangkan untuk berbicara dengan profesional.
                    </p>
                    <button id="retake-btn" class="bg-blue-500 text-white font-semibold py-2 px-6 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105">
                        Ulangi Asesmen
                    </button>
                </div>
            </div>

        </div>
        <footer class="text-center mt-4">
            <p class="text-xs text-slate-400">Dibuat dengan ❤️ untuk refleksi diri.</p>
        </footer>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Data Pertanyaan
            const questions = [
                "Saya percaya bahwa kebanyakan orang akan merendahkan seseorang yang memiliki karakteristik seperti saya.",
                "Saya merasa orang lain berpikir negatif tentang saya karena karakteristik ini.",
                "Kebanyakan orang akan dengan sengaja menghindari berinteraksi dengan seseorang seperti saya.",
                "Saya merasa bahwa karakteristik ini membuat saya diperlakukan secara tidak adil.",
                "Saya khawatir orang lain akan mengetahui tentang karakteristik saya karena takut akan penolakan.",
                "Saya merasa orang lain membuat asumsi yang tidak adil tentang kemampuan saya karena karakteristik ini.",
                "Saya merasa diabaikan atau tidak dianggap penting dalam beberapa situasi sosial karena karakteristik ini.",
                "Saya yakin bahwa karakteristik ini telah menghalangi saya mendapatkan kesempatan tertentu (misalnya pekerjaan, pertemanan, atau promosi).",
                "Orang sering kali memperlakukan saya dengan rasa kasihan yang tidak saya inginkan, bukan dengan hormat.",
                "Saya merasa harus berusaha lebih keras dari orang lain untuk membuktikan diri karena adanya karakteristik ini.",
                "Saya sering mendengar komentar atau lelucon negatif yang merendahkan orang dengan karakteristik seperti saya.",
                "Secara umum, saya merasa masyarakat luas tidak menghargai orang-orang dengan karakteristik seperti saya."
            ];

            // Variabel state aplikasi
            let currentQuestionIndex = 0;
            const userAnswers = new Array(questions.length).fill(null);

            // Elemen DOM
            const characteristicInput = document.getElementById('characteristic');
            const startBtn = document.getElementById('start-btn');
            const introSection = document.getElementById('intro-section');
            const assessmentSection = document.getElementById('assessment-section');
            const resultsSection = document.getElementById('results-section');
            const questionContainer = document.getElementById('question-container');
            const progressBar = document.getElementById('progress-bar');
            const currentQuestionNumber = document.getElementById('current-question-number');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const retakeBtn = document.getElementById('retake-btn');

            // Fungsi untuk mengaktifkan tombol mulai
            characteristicInput.addEventListener('input', () => {
                startBtn.disabled = characteristicInput.value.trim() === '';
            });

            // Memulai asesmen
            startBtn.addEventListener('click', () => {
                introSection.classList.add('hidden');
                assessmentSection.classList.remove('hidden');
                assessmentSection.classList.add('fade-in');
                renderQuestion();
            });

            // Fungsi untuk merender pertanyaan
            const renderQuestion = () => {
                const question = questions[currentQuestionIndex];
                questionContainer.innerHTML = `
                    <div class="fade-in">
                        <p class="text-lg sm:text-xl font-semibold text-center text-slate-800 mb-6">${question}</p>
                        <div class="grid grid-cols-5 gap-2 sm:gap-4 text-center">
                            ${[1,2,3,4,5].map(val => `
                                <div>
                                    <input type="radio" id="scale-${val}" name="scale" value="${val}" class="scale-radio" ${userAnswers[currentQuestionIndex] === val ? 'checked' : ''}>
                                    <label for="scale-${val}" class="scale-label block w-full p-3 sm:p-4 rounded-lg bg-slate-100 font-bold text-slate-700">
                                        ${val}
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        <div class="flex justify-between text-xs sm:text-sm text-slate-500 mt-2 px-1">
                            <span>Sangat Tidak Setuju</span>
                            <span>Sangat Setuju</span>
                        </div>
                    </div>
                `;
                updateProgress();
                updateNavigation();
                
                // Tambah event listener untuk radio button yang baru dibuat
                document.querySelectorAll('input[name="scale"]').forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        userAnswers[currentQuestionIndex] = parseInt(e.target.value);
                        updateNavigation();
                        // Otomatis lanjut ke pertanyaan berikutnya setelah memilih
                        setTimeout(() => {
                            if (currentQuestionIndex < questions.length - 1) {
                                nextBtn.click();
                            }
                        }, 300);
                    });
                });
            };

            // Fungsi untuk memperbarui progress bar dan nomor pertanyaan
            const updateProgress = () => {
                const progressPercentage = ((currentQuestionIndex + 1) / questions.length) * 100;
                progressBar.style.width = `${progressPercentage}%`;
                currentQuestionNumber.textContent = currentQuestionIndex + 1;
            };

            // Fungsi untuk memperbarui tombol navigasi
            const updateNavigation = () => {
                prevBtn.style.visibility = currentQuestionIndex === 0 ? 'hidden' : 'visible';
                nextBtn.disabled = userAnswers[currentQuestionIndex] === null;
                if (currentQuestionIndex === questions.length - 1) {
                    nextBtn.textContent = 'Lihat Hasil';
                } else {
                    nextBtn.textContent = 'Lanjut';
                }
            };

            // Event listener untuk tombol navigasi
            prevBtn.addEventListener('click', () => {
                if (currentQuestionIndex > 0) {
                    currentQuestionIndex--;
                    renderQuestion();
                }
            });

            nextBtn.addEventListener('click', () => {
                if (currentQuestionIndex < questions.length - 1) {
                    currentQuestionIndex++;
                    renderQuestion();
                } else {
                    // Cek jika semua sudah dijawab
                    if (userAnswers.every(answer => answer !== null)) {
                        calculateAndShowResults();
                    }
                }
            });
            
            // Fungsi untuk menghitung dan menampilkan hasil
            const calculateAndShowResults = () => {
                const totalScore = userAnswers.reduce((sum, answer) => sum + answer, 0);
                let interpretation = '';
                let title = '';
                let cardClass = '';

                if (totalScore <= 28) {
                    title = 'Persepsi Rendah';
                    interpretation = 'Anda cenderung memiliki persepsi yang rendah terhadap devaluasi dan diskriminasi terkait karakteristik Anda. Anda mungkin merasa bahwa karakteristik tersebut tidak secara signifikan memengaruhi cara orang lain memperlakukan Anda.';
                    cardClass = 'bg-green-50 border-green-200';
                } else if (totalScore <= 44) {
                    title = 'Persepsi Sedang';
                    interpretation = 'Anda terkadang merasa mengalami perlakuan negatif atau tidak adil, namun mungkin tidak terjadi secara konsisten atau intens. Ada kalanya Anda merasa dihakimi, namun di lain waktu merasa diterima.';
                    cardClass = 'bg-yellow-50 border-yellow-200';
                } else {
                    title = 'Persepsi Tinggi';
                    interpretation = 'Anda memiliki persepsi yang kuat bahwa Anda sering diperlakukan secara negatif, direndahkan, dan didiskriminasi karena karakteristik Anda. Hal ini kemungkinan besar berdampak signifikan pada interaksi sosial dan kesejahteraan emosional Anda.';
                    cardClass = 'bg-red-50 border-red-200';
                }

                document.getElementById('total-score').textContent = totalScore;
                document.getElementById('result-title').textContent = title;
                document.getElementById('result-interpretation').textContent = interpretation;
                document.getElementById('result-card').className = `p-6 rounded-lg border-2 ${cardClass}`;

                assessmentSection.classList.add('hidden');
                resultsSection.classList.remove('hidden');
                resultsSection.classList.add('fade-in');
            };

            // Fungsi untuk mengulang asesmen
            retakeBtn.addEventListener('click', () => {
                currentQuestionIndex = 0;
                userAnswers.fill(null);
                characteristicInput.value = '';
                startBtn.disabled = true;
                
                resultsSection.classList.add('hidden');
                introSection.classList.remove('hidden');
                introSection.classList.add('fade-in');
                nextBtn.textContent = 'Lanjut';
            });
        });
    </script>
</body>
</html>
