# Santrimental API Endpoints (Authentication Disabled)

**⚠️ IMPORTANT: Authentication has been temporarily disabled for all API endpoints for testing purposes.**

## Base URL
```
http://127.0.0.1:8000/api
```

## Available Endpoints

### 📋 Form Management
- **GET** `/forms` - Get all form templates
- **GET** `/forms/{code}` - Get specific form by code
- **GET** `/forms/{code}/responses` - Get user responses for a form
- **POST** `/forms/{code}/submit` - Submit form response

### 📊 Dashboard & Statistics
- **GET** `/dashboard/stats` - Get dashboard statistics
- **GET** `/dashboard/role-data` - Get role-based dashboard data

### 👤 User & Role Management
- **GET** `/user/role` - Get user role information

### 🔐 Authentication (Limited)
- **POST** `/auth/qr` - QR code login (still available)

### 👨‍💼 Admin Endpoints (Now Public)
- **GET** `/admin/users` - Admin users endpoint
- **GET** `/admin/reports` - Admin reports endpoint

### 👨‍🏫 Teacher (Guru) Endpoints (Now Public)
- **GET** `/guru/students` - Teacher students endpoint
- **GET** `/guru/class-reports` - Teacher class reports endpoint

### 👨‍👩‍👧‍👦 Parent (Orangtua) Endpoints (Now Public)
- **GET** `/orangtua/children` - Parent children endpoint

## Example API Calls

### Get All Forms
```bash
curl -X GET "http://127.0.0.1:8000/api/forms" \
  -H "Accept: application/json"
```

### Get Dashboard Stats
```bash
curl -X GET "http://127.0.0.1:8000/api/dashboard/stats" \
  -H "Accept: application/json"
```

### Submit Form Response
```bash
curl -X POST "http://127.0.0.1:8000/api/forms/{code}/submit" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"responses": {"question_1": "answer_1"}}'
```

### PowerShell Examples
```powershell
# Get forms
Invoke-WebRequest -Uri "http://127.0.0.1:8000/api/forms" -Headers @{"Accept"="application/json"}

# Get dashboard stats
Invoke-WebRequest -Uri "http://127.0.0.1:8000/api/dashboard/stats" -Headers @{"Accept"="application/json"}

# Get admin users
Invoke-WebRequest -Uri "http://127.0.0.1:8000/api/admin/users" -Headers @{"Accept"="application/json"}
```

## Response Format

All endpoints return JSON responses in the following format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    // Validation errors if applicable
  }
}
```

## Testing Status
✅ All endpoints are accessible without authentication  
✅ Form management endpoints working  
✅ Dashboard statistics working  
✅ Admin endpoints accessible  
✅ Role-based endpoints accessible  
✅ Error handling working (404 for invalid routes)  

## Notes
- **Security Warning**: This configuration is for development/testing only
- All role-based middleware has been temporarily removed
- Authentication middleware (`auth:sanctum`) has been disabled
- To re-enable authentication, uncomment the middleware groups in `routes/api.php`

## Re-enabling Authentication
To restore authentication, edit `backend/routes/api.php` and:
1. Uncomment the `Route::middleware('auth:sanctum')->group(function () {` line
2. Re-add the closing `});` for the middleware group
3. Move protected routes back inside the middleware group
4. Re-add role-based middleware where needed
