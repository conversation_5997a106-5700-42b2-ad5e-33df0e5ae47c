@extends('tailadmin-base')

@section('title', 'Pilih Assessment - SantriMental')
@section('dashboard-subtitle', 'Assessment Center')
@section('page-title', 'Assessment Kesehatan Mental')
@section('page-description', 'Pilih jenis assessment yang ingin Anda lakukan untuk memahami kondisi kesehatan mental Anda')
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Menu Utama</p>

    <a href="{{ route('dashboard') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Skrining Mental</span>
        <div class="ml-auto">
            <span class="tailadmin-badge primary text-xs">Aktif</span>
        </div>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Riwayat Tes</span>
    </a>
</div>
@endsection

@section('header-actions')
<button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.href='{{ route('dashboard') }}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    Kembali ke Dashboard
</button>
@endsection

@section('breadcrumb')
<a href="{{ route('dashboard') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Dashboard</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">Assessment</span>
@endsection

@section('content')
<!-- Welcome Banner -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
            </svg>
        </div>
        <h2 class="text-3xl font-bold tailadmin-text-primary mb-4">Pilih Assessment Kesehatan Mental</h2>
        <p class="tailadmin-text-secondary text-lg max-w-3xl mx-auto">
            Pilih jenis assessment yang ingin Anda lakukan. Setiap assessment memiliki tujuan dan fokus yang berbeda 
            untuk membantu memahami kondisi kesehatan mental Anda.
        </p>
    </div>
</div>

<!-- Loading State -->
<div id="loading-state" class="text-center py-12">
    <div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
    <p class="tailadmin-text-secondary">Memuat daftar assessment...</p>
</div>

<!-- Assessment Cards -->
<div id="assessments-grid" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Assessment cards will be inserted here -->
</div>

<!-- Empty State -->
<div id="empty-state" class="hidden text-center py-12">
    <div class="tailadmin-card p-12">
        <div class="w-16 h-16 bg-gray-100 dark:bg-dark-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Tidak Ada Assessment</h3>
        <p class="tailadmin-text-secondary mb-4">Belum ada assessment yang tersedia saat ini</p>
        <button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.reload()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Muat Ulang
        </button>
    </div>
</div>

<!-- Quick Stats -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8" id="quick-stats" style="display: none;">
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.1s;">
        <div class="tailadmin-stats-icon primary">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
            </svg>
        </div>
        <p class="tailadmin-stats-label">Assessment Tersedia</p>
        <p class="tailadmin-stats-value" id="available-count">0</p>
        <p class="text-xs tailadmin-text-secondary mt-1">Jenis tes mental</p>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.2s;">
        <div class="tailadmin-stats-icon success">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <p class="tailadmin-stats-label">Waktu Rata-rata</p>
        <p class="tailadmin-stats-value" id="avg-time">15</p>
        <p class="text-xs tailadmin-text-secondary mt-1">Menit per tes</p>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.3s;">
        <div class="tailadmin-stats-icon warning">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <p class="tailadmin-stats-label">Rekomendasi</p>
        <p class="tailadmin-stats-value">SRQ-20</p>
        <p class="text-xs tailadmin-text-secondary mt-1">Untuk pemula</p>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', async () => {
    const loadingState = document.getElementById('loading-state');
    const assessmentsGrid = document.getElementById('assessments-grid');
    const emptyState = document.getElementById('empty-state');
    const quickStats = document.getElementById('quick-stats');

    try {
        // Simulate loading assessments (replace with actual API call)
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock assessment data
        const assessments = [
            {
                code: 'SRQ20',
                name: 'SRQ-20',
                description: 'Self Reporting Questionnaire untuk skrining gangguan mental umum dengan 20 pertanyaan sederhana.',
                time_limit: 15,
                category: 'mental_health',
                questions_count: 20,
                difficulty: 'Mudah'
            },
            {
                code: 'DASS42',
                name: 'DASS-42',
                description: 'Depression Anxiety Stress Scale untuk mengukur tingkat depresi, kecemasan, dan stres.',
                time_limit: 25,
                category: 'psychological',
                questions_count: 42,
                difficulty: 'Sedang'
            },
            {
                code: 'GSE',
                name: 'General Self-Efficacy',
                description: 'Skala untuk mengukur keyakinan diri dalam menghadapi berbagai situasi sulit.',
                time_limit: 10,
                category: 'self_efficacy',
                questions_count: 10,
                difficulty: 'Mudah'
            },
            {
                code: 'MSCS',
                name: 'Cultural Sensitivity Scale',
                description: 'Mengukur sensitivitas budaya dalam konteks multikultural dengan 25 pertanyaan.',
                time_limit: 15,
                category: 'cultural_sensitivity',
                questions_count: 25,
                difficulty: 'Sedang'
            },
            {
                code: 'MHKQ',
                name: 'Mental Health Knowledge',
                description: 'Mengukur pengetahuan tentang kesehatan mental dan gangguan jiwa.',
                time_limit: 20,
                category: 'knowledge',
                questions_count: 20,
                difficulty: 'Sedang'
            }
        ];

        if (assessments.length > 0) {
            displayAssessments(assessments);
            showStats(assessments);
        } else {
            showEmpty();
        }
    } catch (error) {
        console.error('Failed to load assessments:', error);
        showToast('Gagal memuat daftar assessment', 'error');
        showEmpty();
    }

    function displayAssessments(assessments) {
        const assessmentIcons = {
            'SRQ20': '🧠',
            'DASS42': '📊',
            'GSE': '💪',
            'MSCS': '🤝',
            'MHKQ': '📚'
        };

        const assessmentColors = {
            'SRQ20': 'primary',
            'DASS42': 'secondary',
            'GSE': 'success',
            'MSCS': 'warning',
            'MHKQ': 'danger'
        };

        const cardsHTML = assessments.map((assessment, index) => {
            const icon = assessmentIcons[assessment.code] || '📋';
            const colorClass = assessmentColors[assessment.code] || 'primary';
            
            return `
                <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-${colorClass}-100 dark:bg-${colorClass}-900 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">${icon}</span>
                        </div>
                        <h3 class="text-xl font-bold tailadmin-text-primary mb-2">${assessment.name}</h3>
                        <p class="tailadmin-text-secondary text-sm leading-relaxed">${assessment.description}</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="tailadmin-text-secondary">Waktu:</span>
                            <span class="tailadmin-text-primary font-medium">${assessment.time_limit} menit</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="tailadmin-text-secondary">Pertanyaan:</span>
                            <span class="tailadmin-text-primary font-medium">${assessment.questions_count} soal</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="tailadmin-text-secondary">Tingkat:</span>
                            <span class="tailadmin-badge ${colorClass} text-xs">${assessment.difficulty}</span>
                        </div>
                    </div>
                    
                    <button onclick="startAssessment('${assessment.code}')" 
                            class="tailadmin-btn tailadmin-btn-${colorClass} w-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Mulai Assessment
                    </button>
                </div>
            `;
        }).join('');

        assessmentsGrid.innerHTML = cardsHTML;
        showAssessments();
    }

    function showStats(assessments) {
        document.getElementById('available-count').textContent = assessments.length;
        const avgTime = Math.round(assessments.reduce((sum, a) => sum + a.time_limit, 0) / assessments.length);
        document.getElementById('avg-time').textContent = avgTime;
        quickStats.style.display = 'grid';
    }

    function showAssessments() {
        loadingState.classList.add('hidden');
        emptyState.classList.add('hidden');
        assessmentsGrid.classList.remove('hidden');
    }

    function showEmpty() {
        loadingState.classList.add('hidden');
        assessmentsGrid.classList.add('hidden');
        emptyState.classList.remove('hidden');
    }

    // Global function to start assessment
    window.startAssessment = (code) => {
        showToast(`Memulai assessment ${code}...`, 'info');
        setTimeout(() => {
            window.location.href = `/assessment/${code}`;
        }, 1000);
    };
});
</script>
@endpush
