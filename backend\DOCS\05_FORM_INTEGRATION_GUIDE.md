# Form Integration Guide - SantriMental

Panduan lengkap untuk mengintegrasikan form assessment baru ke dalam sistem SantriMental.

## 📋 Form yang Sudah Terintegrasi

### 1. SRQ-20 (Self Report Questionnaire 20)
- **Tipe**: Binary (Ya/Tidak)
- **<PERSON><PERSON><PERSON>**: 20
- **W<PERSON>tu**: 15 menit
- **Scoring**: 0-20 poin
- **Interpretasi**: Normal (0-5), <PERSON><PERSON>n (6-7), High Risk (8-20)

### 2. GSE (General Self-Efficacy Scale)
- **Tipe**: Likert Scale (1-4)
- **Ju<PERSON>lah <PERSON>**: 10
- **Waktu**: 10 menit
- **Scoring**: 10-40 poin
- **<PERSON><PERSON><PERSON><PERSON>**: <PERSON><PERSON> (10-24), <PERSON><PERSON><PERSON> (25-29), <PERSON><PERSON><PERSON> (30-34), Sang<PERSON> Tinggi (35-40)

### 3. MSCS (Multidimensional Scale of Perceived Social Support)
- **Tipe**: Likert Scale (1-7)
- **<PERSON><PERSON>lah <PERSON>**: 12
- **Waktu**: 8 menit
- **<PERSON><PERSON>**: 12-84 poin
- **Interpretasi**: <PERSON><PERSON> (12-36), <PERSON><PERSON><PERSON> (37-60), <PERSON><PERSON>i (61-84)

### 4. MHKQ (Mental Health Knowledge Questionnaire)
- **Tipe**: True/False (Benar/Salah)
- **Jumlah Pertanyaan**: 20
- **Waktu**: 15 menit
- **Scoring**: 0-20 poin
- **Interpretasi**: Rendah (0-10), Cukup (11-15), Tinggi (16-20)

### 5. DASS42 (Depression, Anxiety and Stress Scale)
- **Tipe**: Scale (0-3)
- **Jumlah Pertanyaan**: 21 (simplified version)
- **Waktu**: 20 menit
- **Scoring**: 0-63 poin (total), dengan subscales untuk Depression, Anxiety, Stress
- **Interpretasi**: Normal (0-30), Ringan (31-60), Sedang (61-90), Berat (91-126)

## 🔧 Struktur Database

### Tabel `form_templates`
```sql
- id: Primary key
- code: Unique identifier (SRQ20, GSE, MSCS, MHKQ, DASS42)
- name: Display name
- description: Form description
- category: Form category
- questions: JSON array of questions
- scoring_rules: JSON scoring configuration
- interpretation_rules: JSON interpretation rules
- time_limit: Time limit in minutes
- is_active: Boolean status
- version: Version number
```

### Tabel `form_responses`
```sql
- id: Primary key
- user_id: Foreign key to users
- form_template_id: Foreign key to form_templates
- answers: JSON user responses
- total_score: Calculated score
- status: Interpretation status
- interpretation: Interpretation text
- recommendations: JSON recommendations
- completion_time: Time taken in seconds
```

## 📝 Jenis Scoring yang Didukung

### 1. Binary Sum (`binary_sum`)
Untuk form dengan jawaban Ya/Tidak seperti SRQ-20.

```json
{
  "type": "binary_sum",
  "max_score": 20,
  "questions": {
    "1": {"type": "binary", "yes_score": 1, "no_score": 0}
  }
}
```

### 2. Likert Sum (`likert_sum`)
Untuk form dengan skala Likert seperti GSE dan MSCS.

```json
{
  "type": "likert_sum",
  "max_score": 40,
  "questions": {
    "1": {"type": "scale", "min": 1, "max": 4, "multiplier": 1}
  }
}
```

### 3. Knowledge Sum (`knowledge_sum`)
Untuk form pengetahuan dengan jawaban Benar/Salah seperti MHKQ.

```json
{
  "type": "knowledge_sum",
  "max_score": 20,
  "answer_key": {
    "1": "benar", "2": "salah", "3": "benar"
  }
}
```

### 4. DASS Scale (`dass_scale`)
Untuk form DASS dengan subscales.

```json
{
  "type": "dass_scale",
  "max_score": 126,
  "subscales": {
    "depression": [3, 5, 10, 13, 16, 17, 21],
    "anxiety": [2, 4, 7, 9, 15, 19, 20],
    "stress": [1, 6, 8, 11, 12, 14, 18]
  }
}
```

## 🎨 Frontend Rendering

### Dynamic Form Component
File: `backend/public/js/dynamic-form.js`

Form secara otomatis merender berdasarkan konfigurasi:
- **Binary**: Tombol Ya/Tidak
- **Likert**: Skala angka (1-4, 1-7, dll)
- **Knowledge**: Tombol Benar/Salah
- **DASS**: Skala 0-3 dengan label

### Styling
- Glass morphism design
- Responsive layout
- Smooth animations
- Color-coded by assessment type

## 🔄 Cara Menambah Form Baru

### 1. Buat Konfigurasi di Seeder
```php
FormTemplate::updateOrCreate(
    ['code' => 'NEW_FORM'],
    [
        'code' => 'NEW_FORM',
        'name' => 'New Assessment Form',
        'description' => 'Description of the new form',
        'category' => 'assessment_category',
        'questions' => [
            'Question 1',
            'Question 2',
            // ... more questions
        ],
        'scoring_rules' => [
            'type' => 'binary_sum', // or other types
            'max_score' => 10,
            // ... scoring configuration
        ],
        'interpretation_rules' => [
            [
                'min_score' => 0,
                'max_score' => 5,
                'status' => 'normal',
                'interpretation' => 'Normal condition',
                'recommendations' => ['Recommendation 1', 'Recommendation 2']
            ]
            // ... more interpretation rules
        ],
        'time_limit' => 10,
        'is_active' => true,
        'version' => 1
    ]
);
```

### 2. Jalankan Seeder
```bash
php artisan db:seed --class=FormTemplateSeeder
```

### 3. Update Frontend (Opsional)
Tambahkan icon dan warna di `assessments.blade.php`:
```javascript
const assessmentIcons = {
    'NEW_FORM': '🆕'
};

const assessmentColors = {
    'NEW_FORM': 'from-purple-500 to-pink-500'
};
```

### 4. Update Instructions (Opsional)
Tambahkan instruksi khusus di `dynamic-form.js`:
```javascript
const instructionsMap = {
    'NEW_FORM': 'Instruksi khusus untuk form baru'
};
```

## 🧪 Testing

### Manual Testing
1. Buka `/assessments`
2. Pilih form yang ingin ditest
3. Isi semua pertanyaan
4. Submit dan periksa hasil
5. Verifikasi scoring dan interpretasi

### API Testing
```bash
# Get form list
curl -X GET http://127.0.0.1:8000/api/forms \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get specific form
curl -X GET http://127.0.0.1:8000/api/forms/GSE \
  -H "Authorization: Bearer YOUR_TOKEN"

# Submit response
curl -X POST http://127.0.0.1:8000/api/forms/GSE/submit \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"answers": {"1": 4, "2": 3}, "completion_time": 120}'
```

## 📊 Monitoring & Analytics

### Dashboard Integration
- Form responses otomatis terintegrasi dengan dashboard
- Statistik real-time untuk semua form
- Grafik tren skor dari waktu ke waktu
- Filter berdasarkan jenis assessment

### Data Export
- Semua response tersimpan dalam format JSON
- Mudah untuk export dan analisis
- Audit trail lengkap

## 🔒 Security & Validation

### Input Validation
- Semua input divalidasi di backend
- CSRF protection
- XSS prevention
- SQL injection protection

### Access Control
- Authentication required
- User-specific responses
- Admin access untuk form management

## 📈 Performance

### Optimizations
- Lazy loading untuk form besar
- Caching untuk form templates
- Optimized database queries
- Compressed assets

### Scalability
- Database schema yang fleksibel
- API yang RESTful
- Horizontal scaling ready

## 🎯 Best Practices

### Form Design
1. Maksimal 50 pertanyaan per form
2. Instruksi yang jelas dan mudah dipahami
3. Validasi yang komprehensif
4. Feedback yang informatif

### Scoring
1. Dokumentasikan algoritma scoring
2. Validasi dengan referensi ilmiah
3. Test dengan data sample
4. Review interpretasi secara berkala

### User Experience
1. Progress indicator
2. Auto-save (future feature)
3. Mobile-friendly design
4. Accessibility compliance

---

**Catatan**: Sistem ini dirancang untuk fleksibilitas maksimum. Form baru dapat ditambahkan tanpa mengubah kode frontend atau backend, hanya dengan menambahkan konfigurasi di database.
